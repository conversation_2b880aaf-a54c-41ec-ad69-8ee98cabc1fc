"use client";

import React, { useState, useTransition } from "react";
import Link from "next/link";
import { Button, Chip, Tooltip } from "@nextui-org/react";
import toast from "react-hot-toast";
import { CustomTable } from "../custom-table";
import { Icon } from "../icon";
import { PackagingOption, deletePackagingOption, togglePackagingOptionStatus } from "@/actions/packaging-options";

interface PackagingOptionsTableProps {
  data: PackagingOption[];
  meta: PaginationMeta;
  error?: string | null;
}

export default function PackagingOptionsTable({ data, meta, error }: PackagingOptionsTableProps) {
  const [isPending, startTransition] = useTransition();
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  const handleToggleStatus = async (option: PackagingOption) => {
    setLoadingStates(prev => ({ ...prev, [option.id]: true }));
    
    startTransition(async () => {
      try {
        await togglePackagingOptionStatus(option.id, !option.active);
        toast.success(`Packaging option ${!option.active ? 'activated' : 'deactivated'} successfully`);
      } catch (error) {
        console.error("Error toggling status:", error);
        toast.error("Failed to update packaging option status");
      } finally {
        setLoadingStates(prev => ({ ...prev, [option.id]: false }));
      }
    });
  };

  const handleDelete = async (option: PackagingOption) => {
    if (!confirm(`Are you sure you want to delete "${option.name}"? This action cannot be undone.`)) {
      return;
    }

    setLoadingStates(prev => ({ ...prev, [option.id]: true }));
    
    startTransition(async () => {
      try {
        await deletePackagingOption(option.id);
        toast.success("Packaging option deleted successfully");
      } catch (error: any) {
        console.error("Error deleting packaging option:", error);
        if (error.message?.includes("associated with products")) {
          toast.error("Cannot delete packaging option that is currently associated with products");
        } else {
          toast.error("Failed to delete packaging option");
        }
      } finally {
        setLoadingStates(prev => ({ ...prev, [option.id]: false }));
      }
    });
  };

  const formatPrice = (price: number) => {
    return `KES ${(price / 100).toFixed(2)}`;
  };

  const columns = [
    {
      name: "Name",
      uid: "name",
      sortable: true,
      renderCell: (option: PackagingOption) => (
        <div className="flex flex-col">
          <p className="font-semibold text-default-900">{option.name}</p>
          {option.description && (
            <p className="text-sm text-default-500 line-clamp-2">{option.description}</p>
          )}
        </div>
      ),
    },
    {
      name: "Price",
      uid: "price",
      sortable: true,
      renderCell: (option: PackagingOption) => (
        <span className="font-medium text-default-900">
          {formatPrice(option.price)}
        </span>
      ),
    },
    {
      name: "Status",
      uid: "active",
      sortable: true,
      renderCell: (option: PackagingOption) => (
        <Chip
          color={option.active ? "success" : "default"}
          variant="flat"
          size="sm"
        >
          {option.active ? "Active" : "Inactive"}
        </Chip>
      ),
    },
    {
      name: "Created",
      uid: "createdAt",
      sortable: true,
      renderCell: (option: PackagingOption) => (
        <span className="text-sm text-default-500">
          {new Date(option.createdAt).toLocaleDateString()}
        </span>
      ),
    },
    {
      name: "Actions",
      uid: "actions",
      renderCell: (option: PackagingOption) => (
        <div className="flex items-center gap-2">
          <Tooltip content="Edit packaging option">
            <Button
              as={Link}
              href={`/products/packaging-options/${option.id}/edit`}
              isIconOnly
              size="sm"
              variant="light"
              color="primary"
            >
              <Icon name="icon-[heroicons--pencil-20-solid]" classNames="h-4 w-4" />
            </Button>
          </Tooltip>
          
          <Tooltip content={option.active ? "Deactivate" : "Activate"}>
            <Button
              isIconOnly
              size="sm"
              variant="light"
              color={option.active ? "warning" : "success"}
              isLoading={loadingStates[option.id]}
              onPress={() => handleToggleStatus(option)}
            >
              <Icon 
                name={option.active ? "icon-[heroicons--eye-slash-20-solid]" : "icon-[heroicons--eye-20-solid]"} 
                classNames="h-4 w-4" 
              />
            </Button>
          </Tooltip>
          
          <Tooltip content="Delete packaging option" color="danger">
            <Button
              isIconOnly
              size="sm"
              variant="light"
              color="danger"
              isLoading={loadingStates[option.id]}
              onPress={() => handleDelete(option)}
            >
              <Icon name="icon-[heroicons--trash-20-solid]" classNames="h-4 w-4" />
            </Button>
          </Tooltip>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-4">
      {/* Error Alert */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start gap-3">
            <Icon name="icon-[heroicons--exclamation-triangle-20-solid]" classNames="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-red-800 font-medium mb-1">Error loading packaging options</h3>
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      <CustomTable
        title="Packaging Options"
        columns={columns}
        data={data}
        meta={meta}
        filter={[
          {
            column: "active",
            displayName: "Status",
            values: [
              { name: "Active", value: "true" },
              { name: "Inactive", value: "false" },
            ],
          },
        ]}
        action={
          <div className="flex items-center gap-3">
            <Button
              as={Link}
              href="/products/packaging-options/create"
              color="primary"
              startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-4 w-4" />}
            >
              Add Packaging Option
            </Button>
          </div>
        }
      />
    </div>
  );
}
