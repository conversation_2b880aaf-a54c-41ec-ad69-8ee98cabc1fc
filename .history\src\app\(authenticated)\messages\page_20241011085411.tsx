import { api } from "@/lib/api";
import PaginatedTable from "@/components/table";
import Broadcast from "./create";
import { revalidatePath } from "next/cache";
import { format } from "date-fns";
import { auth } from "@/auth";

export const metadata = {
  title: "Broadcast messages",
};

export default async function MessageChat() {
  const messages = await api.get<PaginatedData<Message>>("messages");
  const session = await auth();

  const storeMessage = async (message: FormData) => {
    "use server";

    message.append("userId", session?.user.id!);
    message.append("vendorId", session?.vendor?.id as string);
    message.append("branchId", session?.branch?.id as string);

    try {
      await api.post("messages", message);
      revalidatePath("/messages");
    } catch (error) {
      console.error("Failed to store the message:", error);
      // Optionally, display an error message to the user
    }
  };

  const deleteMessage = async (messageId: string) => {
    "use server";

    try {
      await api.destroy(`messages/${messageId}`);
      revalidatePath("/messages");
    } catch (error) {
      console.error("Failed to delete the message:", error);
      // Optionally, display an error message to the user
    }
  };

  return (
    <div className="flex h-screen flex-col">
      {messages && (
        <PaginatedTable<Message>
          records={messages}
          columns={[
            {
              id: "details",
              title: "Content",
              render: (message) => <>{message.template?.content}</>,
            },
            {
              id: "author",
              title: "Author",
              render: (message) => <>{message.author?.name}</>,
            },
            {
              id: "template.name",
              title: "Template",
              render: (message) => <>{message.template?.name}</>,
            },
            {
              id: "createdAt",
              title: "Sent",
              render: (message) => (
                <>{format(new Date(message.createdAt), "EEE, MMM dd, yyyy")}</>
              ),
            },
            {
              id: "actions",
              title: "",
              render: (message) => (
                <form
                  action={async () => {
                    "use server";

                    try {
                      await api.destroy(`messages/${message.id}`);
                      revalidatePath("/messages");
                    } catch (error) {
                      console.error("Failed to delete the message:", error);
                    }
                  }}
                >
                  <button type="submit" className="text-red-600">
                    Delete
                  </button>
                </form>
              ),
            },
          ]}
          title="Messages"
          path="/path"
          tools={
            <div className="flex items-center justify-between p-4">
              <p>
                Create and manage your messages. You can use them to send
                messages to your customers.
              </p>

              <Broadcast storeMessage={storeMessage} />
            </div>
          }
        />
      )}
    </div>
  );
}
