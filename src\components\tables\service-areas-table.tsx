"use client";

import React from "react";
import { CustomTable } from "../custom-table";
import Link from "next/link";
import { Chip, Switch } from "@nextui-org/react";
import { Icon } from "../icon";
import toast from "react-hot-toast";
import { ServiceArea } from "@/types";
// Using global PaginationMeta type from next-models.d.ts

interface ServiceAreasTableProps {
  data: ServiceArea[];
  meta: PaginationMeta;
  deleteAction: (data: FormData) => Promise<void>;
  toggleActiveAction: (serviceAreaId: string, active: boolean) => Promise<void>;
  vendorId: string;
}

export default function ServiceAreasTable({
  data,
  meta,
  deleteAction,
  toggleActiveAction,
  vendorId,
}: ServiceAreasTableProps) {
  const onDeleteSubmit = async (id: string) => {
    const formData = new FormData();
    formData.append("id", id);

    toast.promise(deleteAction(formData), {
      loading: "Deleting service area...",
      success: "Service area deleted!",
      error: "Failed to delete service area",
    });
  };

  const onToggleActive = async (serviceAreaId: string, currentActive: boolean) => {
    toast.promise(toggleActiveAction(serviceAreaId, !currentActive), {
      loading: `${currentActive ? 'Deactivating' : 'Activating'} service area...`,
      success: `Service area ${currentActive ? 'deactivated' : 'activated'}!`,
      error: `Failed to ${currentActive ? 'deactivate' : 'activate'} service area`,
    });
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'circle':
        return <Icon name="icon-[mingcute--location-line]" classNames="text-blue-500" />;
      case 'polygon':
        return <Icon name="icon-[mingcute--polygon-line]" classNames="text-green-500" />;
      case 'administrative':
        return <Icon name="icon-[mingcute--building-2-line]" classNames="text-purple-500" />;
      default:
        return <Icon name="icon-[mingcute--map-pin-line]" classNames="text-gray-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'circle':
        return 'primary';
      case 'polygon':
        return 'success';
      case 'administrative':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const formatCoverage = (serviceArea: ServiceArea) => {
    switch (serviceArea.type) {
      case 'circle':
        return `${serviceArea.radius}km radius`;
      case 'polygon':
        return 'Custom polygon';
      case 'administrative':
        return serviceArea.administrative_areas?.length 
          ? `${serviceArea.administrative_areas.length} area(s)`
          : 'Administrative areas';
      default:
        return 'Unknown';
    }
  };

  return (
    <CustomTable
      title="Service Areas"
      action={`/service-areas/create`}
      columns={[
        {
          name: "Service Area",
          uid: "name",
          sortable: true,
          renderCell: (serviceArea: ServiceArea) => (
            <div className="flex items-center space-x-3">
              {getTypeIcon(serviceArea.type)}
              <div>
                <p className="font-semibold">{serviceArea.name}</p>
                {serviceArea.description && (
                  <p className="text-sm text-default-500">{serviceArea.description}</p>
                )}
              </div>
            </div>
          ),
        },
        {
          name: "Type",
          uid: "type",
          sortable: true,
          renderCell: (serviceArea: ServiceArea) => (
            <Chip
              size="sm"
              variant="flat"
              color={getTypeColor(serviceArea.type) as any}
              className="capitalize"
            >
              {serviceArea.type}
            </Chip>
          ),
        },
        {
          name: "Coverage",
          uid: "coverage",
          renderCell: (serviceArea: ServiceArea) => (
            <span className="text-sm">{formatCoverage(serviceArea)}</span>
          ),
        },
        {
          name: "Priority",
          uid: "priority",
          sortable: true,
          renderCell: (serviceArea: ServiceArea) => (
            <Chip size="sm" variant="bordered">
              {serviceArea.priority || 0}
            </Chip>
          ),
        },
        {
          name: "Branch",
          uid: "branch",
          renderCell: (serviceArea: ServiceArea) => (
            <span className="text-sm">
              {serviceArea.branch?.name || 'No branch'}
            </span>
          ),
        },
        {
          name: "Active",
          uid: "active",
          sortable: true,
          renderCell: (serviceArea: ServiceArea) => (
            <Switch
              size="sm"
              isSelected={serviceArea.active}
              onValueChange={(checked) => onToggleActive(serviceArea.id, serviceArea.active)}
              color="success"
            />
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (serviceArea: ServiceArea) => (
            <div className="flex w-fit items-center gap-3">
              <Link href={`/service-areas/${serviceArea.id}`}>
                <Icon
                  name="icon-[mingcute--eye-line]"
                  classNames="text-primary hover:text-primary-600 cursor-pointer"
                />
              </Link>
              <Link href={`/service-areas/${serviceArea.id}/edit`}>
                <Icon
                  name="icon-[mage--edit-pen-fill]"
                  classNames="text-primary hover:text-primary-600 cursor-pointer"
                />
              </Link>
              <button
                onClick={() => {
                  if (window.confirm("Are you sure you want to delete this service area? This action cannot be undone.")) {
                    onDeleteSubmit(serviceArea.id);
                  }
                }}
                className="text-red-500 hover:text-red-600"
              >
                <Icon name="icon-[mingcute--delete-2-line]" />
              </button>
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
      filter={{
        column: "type",
        displayName: "Type",
        values: [
          { name: "Circle", value: "circle" },
          { name: "Polygon", value: "polygon" },
          { name: "Administrative", value: "administrative" },
        ],
      }}
    />
  );
}
