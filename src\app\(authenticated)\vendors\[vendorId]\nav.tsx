"use client";

import { Tabs, Tab } from "@nextui-org/react";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface VendorNavProps {
  vendorId: string;
}

export default function VendorNav({ vendorId }: VendorNavProps) {
  const pathname = usePathname();

  const tabs = [
    {
      id: "overview",
      label: "Overview",
      href: `/vendors/${vendorId}`,
    },
    {
      id: "branches",
      label: "Branches",
      href: `/vendors/${vendorId}/branches`,
    },
    {
      id: "products",
      label: "Products",
      href: `/vendors/${vendorId}/products`,
      children: [
        {
          id: "bulk-upload",
          label: "Bulk Upload",
          href: `/vendors/${vendorId}/products/bulk-upload`,
        },
      ],
    },
    {
      id: "tasks",
      label: "Tasks",
      href: `/vendors/${vendorId}/tasks`,
    },
    {
      id: "services",
      label: "Services",
      href: `/vendors/${vendorId}/services`,
    },
  ];

  const currentTab = tabs.find((tab) => pathname.includes(tab.id)) || tabs[0];

  return (
    <div className="space-y-4">
      <Tabs
        aria-label="Vendor sections"
        selectedKey={currentTab.id}
        className="w-full"
        variant="underlined"
      >
        {tabs.map((tab) => (
          <Tab
            key={tab.id}
            title={
              <Link href={tab.href} className="min-w-[100px] px-4">
                {tab.label}
              </Link>
            }
          />
        ))}
      </Tabs>

      {/* Sub-navigation for products section */}
      {currentTab.id === "products" && (
        <div className="flex gap-4 px-4">
          {tabs
            .find((tab) => tab.id === "products")
            ?.children?.map((child) => (
              <Link
                key={child.id}
                href={child.href}
                className={`text-sm ${
                  pathname.includes(child.id)
                    ? "text-primary font-medium"
                    : "text-default-500 hover:text-primary"
                }`}
              >
                {child.label}
              </Link>
            ))}
        </div>
      )}
    </div>
  );
}