"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import {
  Card,
  Card<PERSON>ody,
  CardHeader,
  Input,
  Textarea,
  Select,
  SelectItem,
  Switch,
  Button,
  Divider,
} from "@nextui-org/react";
import { Branch, ServiceArea } from "@/types";
import toast from "react-hot-toast";

interface ServiceAreaFormProps {
  action: (data: FormData) => Promise<void>;
  branches: Branch[];
  vendorId: string;
  serviceArea?: ServiceArea;
  isEdit?: boolean;
}

interface ServiceAreaFormData {
  name: string;
  description: string;
  type: 'circle' | 'polygon' | 'administrative';
  radius?: number;
  branch_id: string;
  priority: number;
  active: boolean;
  estimated_delivery_time?: number;
  delivery_fee?: number;
  special_instructions?: string;
}

export default function ServiceAreaForm({
  action,
  branches,
  vendorId,
  serviceArea,
  isEdit = false,
}: ServiceAreaFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedType, setSelectedType] = useState<string>(
    serviceArea?.type || 'circle'
  );

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<ServiceAreaFormData>({
    defaultValues: {
      name: serviceArea?.name || '',
      description: serviceArea?.description || '',
      type: serviceArea?.type || 'circle',
      radius: serviceArea?.radius || 5,
      branch_id: serviceArea?.branch_id || '',
      priority: serviceArea?.priority || 1,
      active: serviceArea?.active ?? true,
      estimated_delivery_time: serviceArea?.meta?.estimated_delivery_time || 30,
      delivery_fee: serviceArea?.meta?.delivery_fee || 0,
      special_instructions: serviceArea?.meta?.special_instructions || '',
    },
  });

  const watchedType = watch('type');

  const onSubmit = async (data: ServiceAreaFormData) => {
    setIsSubmitting(true);
    
    try {
      const formData = new FormData();
      
      // Basic fields
      formData.append('name', data.name);
      formData.append('description', data.description);
      formData.append('type', data.type);
      formData.append('branch_id', data.branch_id);
      formData.append('priority', data.priority.toString());
      formData.append('active', data.active.toString());

      // Type-specific fields
      if (data.type === 'circle' && data.radius) {
        formData.append('radius', data.radius.toString());
      }

      // Meta data
      const meta = {
        estimated_delivery_time: data.estimated_delivery_time,
        delivery_fee: data.delivery_fee,
        special_instructions: data.special_instructions,
      };
      formData.append('meta', JSON.stringify(meta));

      await action(formData);
      
      toast.success(
        isEdit ? 'Service area updated successfully!' : 'Service area created successfully!'
      );
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error(
        isEdit ? 'Failed to update service area' : 'Failed to create service area'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Basic Information</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <Input
            label="Service Area Name"
            placeholder="e.g., Downtown Coverage"
            isRequired
            {...register('name', { required: 'Service area name is required' })}
            errorMessage={errors.name?.message}
            isInvalid={!!errors.name}
          />

          <Textarea
            label="Description"
            placeholder="Describe this service area..."
            {...register('description')}
            errorMessage={errors.description?.message}
            isInvalid={!!errors.description}
          />

          <Select
            label="Branch"
            placeholder="Select a branch"
            isRequired
            {...register('branch_id', { required: 'Branch selection is required' })}
            errorMessage={errors.branch_id?.message}
            isInvalid={!!errors.branch_id}
          >
            {branches.map((branch) => (
              <SelectItem key={branch.id} value={branch.id}>
                {branch.name}
              </SelectItem>
            ))}
          </Select>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              type="number"
              label="Priority"
              placeholder="1"
              min={0}
              max={100}
              {...register('priority', { 
                required: 'Priority is required',
                min: { value: 0, message: 'Priority must be 0 or greater' },
                max: { value: 100, message: 'Priority must be 100 or less' }
              })}
              errorMessage={errors.priority?.message}
              isInvalid={!!errors.priority}
            />

            <div className="flex items-center space-x-2 pt-6">
              <Switch
                {...register('active')}
                defaultSelected={serviceArea?.active ?? true}
                color="success"
              >
                Active
              </Switch>
            </div>
          </div>
        </CardBody>
      </Card>

      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Coverage Configuration</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <Select
            label="Coverage Type"
            placeholder="Select coverage type"
            isRequired
            selectedKeys={[watchedType]}
            onSelectionChange={(keys) => {
              const selectedKey = Array.from(keys)[0] as string;
              setSelectedType(selectedKey);
              setValue('type', selectedKey as any);
            }}
            {...register('type', { required: 'Coverage type is required' })}
            errorMessage={errors.type?.message}
            isInvalid={!!errors.type}
          >
            <SelectItem key="circle" value="circle">
              Circle - Radius-based coverage
            </SelectItem>
            <SelectItem key="polygon" value="polygon">
              Polygon - Custom boundary (Coming Soon)
            </SelectItem>
            <SelectItem key="administrative" value="administrative">
              Administrative - By regions (Coming Soon)
            </SelectItem>
          </Select>

          {watchedType === 'circle' && (
            <Input
              type="number"
              label="Radius (km)"
              placeholder="5"
              min={0.1}
              max={100}
              step={0.1}
              isRequired
              {...register('radius', { 
                required: 'Radius is required for circle type',
                min: { value: 0.1, message: 'Radius must be at least 0.1 km' },
                max: { value: 100, message: 'Radius must be 100 km or less' }
              })}
              errorMessage={errors.radius?.message}
              isInvalid={!!errors.radius}
            />
          )}

          {watchedType === 'polygon' && (
            <div className="p-4 bg-warning-50 border border-warning-200 rounded-lg">
              <p className="text-warning-800 text-sm">
                Polygon coverage with interactive map drawing tools is coming soon.
              </p>
            </div>
          )}

          {watchedType === 'administrative' && (
            <div className="p-4 bg-warning-50 border border-warning-200 rounded-lg">
              <p className="text-warning-800 text-sm">
                Administrative area selection is coming soon.
              </p>
            </div>
          )}
        </CardBody>
      </Card>

      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Delivery Settings</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              type="number"
              label="Estimated Delivery Time (minutes)"
              placeholder="30"
              min={1}
              max={480}
              {...register('estimated_delivery_time', {
                min: { value: 1, message: 'Must be at least 1 minute' },
                max: { value: 480, message: 'Must be 8 hours or less' }
              })}
              errorMessage={errors.estimated_delivery_time?.message}
              isInvalid={!!errors.estimated_delivery_time}
            />

            <Input
              type="number"
              label="Delivery Fee"
              placeholder="0"
              min={0}
              step={0.01}
              startContent={
                <div className="pointer-events-none flex items-center">
                  <span className="text-default-400 text-small">KES</span>
                </div>
              }
              {...register('delivery_fee', {
                min: { value: 0, message: 'Delivery fee cannot be negative' }
              })}
              errorMessage={errors.delivery_fee?.message}
              isInvalid={!!errors.delivery_fee}
            />
          </div>

          <Textarea
            label="Special Instructions"
            placeholder="Any special delivery instructions for this area..."
            {...register('special_instructions')}
          />
        </CardBody>
      </Card>

      <Divider />

      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="light"
          onPress={() => window.history.back()}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          color="primary"
          isLoading={isSubmitting}
          isDisabled={isSubmitting}
        >
          {isEdit ? 'Update Service Area' : 'Create Service Area'}
        </Button>
      </div>
    </form>
  );
}
