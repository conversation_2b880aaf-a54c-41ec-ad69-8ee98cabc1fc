export type ProductArchetype = 'PHYSICAL' | 'DIGITAL' | 'SERVICE';

export type DigitalDeliveryMethod = 'emailLink' | 'inAppAccess' | 'licenseKey' | null;

export interface ProductFulfillmentSetting {
  id: number;
  productId: string | null;
  archetype: ProductArchetype;
  isPayable: boolean;
  hasForm: boolean;
  isInformational: boolean;
  isDeliverable: boolean;
  isPickup: boolean;
  physicalConsumptionIsOnsite: boolean;
  isDownloadable: boolean;
  digitalDeliveryMethod: DigitalDeliveryMethod;
  isSchedulable: boolean;
  serviceIsOnsite: boolean;
  serviceIsRemote: boolean;
  serviceIsDynamicQuery: boolean;
  preorderAllowed: boolean;
  scheduleAllowed: boolean;
  createdAt: string;
  updatedAt: string;
  product?: Product;
}

export interface ProductFulfillmentPayload {
  archetype?: ProductArchetype;
  isPayable?: boolean;
  hasForm?: boolean;
  isInformational?: boolean;
  isDeliverable?: boolean;
  isPickup?: boolean;
  physicalConsumptionIsOnsite?: boolean;
  isDownloadable?: boolean;
  digitalDeliveryMethod?: DigitalDeliveryMethod;
  isSchedulable?: boolean;
  serviceIsOnsite?: boolean;
  serviceIsRemote?: boolean;
  serviceIsDynamicQuery?: boolean;
  preorderAllowed?: boolean;
  scheduleAllowed?: boolean;
}

// Archetype mapping from product type to API archetype
export const archetypeMap: Record<string, ProductArchetype> = {
  "Physical": "PHYSICAL",
  "Digital": "DIGITAL", 
  "Service": "SERVICE"
};

// Settings that are relevant for each archetype
export const relevantSettings: Record<ProductArchetype, (keyof ProductFulfillmentPayload)[]> = {
  PHYSICAL: ["isDeliverable", "isPickup", "physicalConsumptionIsOnsite", "preorderAllowed", "hasForm"],
  DIGITAL: ["isDownloadable", "digitalDeliveryMethod", "preorderAllowed", "isInformational"],
  SERVICE: ["isSchedulable", "serviceIsOnsite", "serviceIsRemote", "serviceIsDynamicQuery", "scheduleAllowed", "hasForm"]
};

// Default values for each archetype
export const typeDefaults: Record<string, Partial<ProductFulfillmentPayload>> = {
  Physical: {
    isDeliverable: true,
    isPickup: true,
    isPayable: true,
    preorderAllowed: true,
    scheduleAllowed: false,
    hasForm: false,
    physicalConsumptionIsOnsite: false
  },
  Digital: {
    isDownloadable: true,
    digitalDeliveryMethod: "emailLink",
    isPayable: true,
    preorderAllowed: true,
    scheduleAllowed: false,
    isInformational: false
  },
  Service: {
    isSchedulable: true,
    serviceIsRemote: true,
    isPayable: true,
    scheduleAllowed: true,
    hasForm: true,
    serviceIsOnsite: false,
    serviceIsDynamicQuery: false
  }
}; 