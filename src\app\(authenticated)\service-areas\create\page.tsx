import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect } from "next/navigation";
import ServiceAreaForm from "@/components/forms/service-area-form";
import { revalidatePath } from "next/cache";
import { PaginatedData, Branch } from "@/types";
import { Button } from "@nextui-org/react";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

const fetchBranches = cache(
  (vendorId: string) =>
    api.get<PaginatedData<Branch>>(`vendors/${vendorId}/branches`),
);

export const generateMetadata = async () => {
  return {
    title: "Create Service Area",
    description: "Create a new delivery service area",
    keywords: ["service area", "delivery", "coverage", "create"],
  };
};

export default async function CreateServiceAreaPage() {
  const session = await auth();

  // Check if user is authenticated and has vendor access
  if (!session) {
    redirect('/login');
  }

  // Get vendor ID from session
  const vendorId = session?.vendor?.id || session?.branch?.vendorId;
  
  if (!vendorId) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            No Vendor Access
          </h2>
          <p className="text-gray-600">
            You need to be associated with a vendor to create service areas.
          </p>
        </div>
      </div>
    );
  }

  // Fetch vendor branches for the form
  const branches = await fetchBranches(vendorId);

  const createServiceArea = async (data: FormData) => {
    "use server";

    try {
      await api.post(`vendors/${vendorId}/service-areas`, data);
      revalidatePath("/service-areas");
      redirect("/service-areas");
    } catch (error) {
      console.error("Failed to create service area:", error);
      throw error;
    }
  };

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <Button
          as={Link}
          href="/service-areas"
          variant="light"
          startContent={<ArrowLeft className="size-4" />}
          className="mb-4"
        >
          Back to Service Areas
        </Button>
        
        <h1 className="text-2xl font-bold text-gray-900">Create Service Area</h1>
        <p className="text-gray-600 mt-1">
          Define a new delivery coverage area for your business
        </p>
      </div>

      <div className="max-w-4xl">
        <ServiceAreaForm
          action={createServiceArea}
          branches={branches?.data || []}
          vendorId={vendorId}
        />
      </div>
    </div>
  );
}
