import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import Link from "next/link";
import { cache } from "react";

const fetchNotification = cache((id: string) =>
  api.get<DatabaseNotification>(`notifications/${id}`),
);

export const generateMetadata = async ({
  params,
}: {
  params: {
    notificationId: string;
  };
}): Promise<Record<string, any>> => {
  const notification = await fetchNotification(params.notificationId);

  return {
    title: notification?.data.title,
    description: "Notification",
    keywords: ["notification"],
  };
};

export default async function page({
  params,
}: {
  params: {
    notificationId: string;
  };
}) {
  const notification = await fetchNotification(params.notificationId);

  api
    .put(`notifications/${params.notificationId}`, {
      read_at: new Date(),
    })
    .then(() => {
      revalidatePath(`/notifications/${params.notificationId}`);
    });

  return (
    <div className="p-4">
      <p>{notification?.data.body}</p>

      {notification?.data.actions && (
        <>
          {notification.data.actions.map((action: any) => (
            <div key={action.id} className="flex items-center gap-2">
              <Link
              href={"nkkjjkjhinn"}
                // href={`${Object.values(action.args).reduce(
                //   (p, v) => `${p}${v}`,
                //   action.screen.replace(/\[.+?]/g, ""),
                // )}`}
                className="mt-5 inline-flex rounded-lg bg-primary px-5 py-3 text-sm font-semibold text-white decoration-2"
              >
                {action.label}
              </Link>
            </div>
          ))}
        </>
      )}
    </div>
  );
}
