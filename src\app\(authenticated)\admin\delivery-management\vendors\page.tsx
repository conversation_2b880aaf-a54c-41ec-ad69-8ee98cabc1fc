import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import AdminDeliveryVendorsTable from "@/components/admin/delivery/admin-delivery-vendors-table";
import { PaginatedData } from "@/types";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface DeliveryVendor {
  id: string;
  name: string;
  email: string;
  phone: string;
  verification_status: 'pending' | 'verified' | 'rejected' | 'suspended';
  service_areas_count: number;
  coverage_regions: string[];
  performance_metrics: {
    delivery_success_rate: number;
    average_delivery_time: number;
    total_deliveries: number;
    customer_rating: number;
  };
  last_activity: string;
  created_at: string;
  updated_at: string;
}

const fetchDeliveryVendors = cache(
  (searchParams?: Record<string, string>) =>
    api.get<PaginatedData<DeliveryVendor>>(
      'admin/delivery-management/vendors',
      searchParams,
    ),
);

export const generateMetadata = async () => {
  return {
    title: "Delivery Vendors Management",
    description: "Manage delivery vendors, verification status, and performance",
    keywords: ["admin", "delivery", "vendors", "management"],
  };
};

export default async function AdminDeliveryVendorsPage({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  // Fetch vendors data
  const vendors = await fetchDeliveryVendors(searchParams).catch(() => null);

  // Demo data fallback
  const demoVendors: PaginatedData<DeliveryVendor> = {
    data: [
      {
        id: "v1",
        name: "FastDelivery Co.",
        email: "<EMAIL>",
        phone: "+254700123456",
        verification_status: "verified",
        service_areas_count: 12,
        coverage_regions: ["Nairobi CBD", "Westlands", "Karen"],
        performance_metrics: {
          delivery_success_rate: 96.5,
          average_delivery_time: 25,
          total_deliveries: 1250,
          customer_rating: 4.8,
        },
        last_activity: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(),
        updated_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
      },
      {
        id: "v2",
        name: "QuickCourier",
        email: "<EMAIL>",
        phone: "+254700234567",
        verification_status: "pending",
        service_areas_count: 8,
        coverage_regions: ["Kiambu", "Thika"],
        performance_metrics: {
          delivery_success_rate: 92.1,
          average_delivery_time: 32,
          total_deliveries: 890,
          customer_rating: 4.5,
        },
        last_activity: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(),
        updated_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
      },
      {
        id: "v3",
        name: "SpeedyService",
        email: "<EMAIL>",
        phone: "+254700345678",
        verification_status: "rejected",
        service_areas_count: 3,
        coverage_regions: ["Eastlands"],
        performance_metrics: {
          delivery_success_rate: 78.5,
          average_delivery_time: 45,
          total_deliveries: 234,
          customer_rating: 3.9,
        },
        last_activity: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),
        created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 45).toISOString(),
        updated_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),
      },
    ],
    meta: {
      total: 3,
      perPage: 10,
      currentPage: 1,
      lastPage: 1,
      firstPage: 1,
      firstPageUrl: "/admin/delivery-management/vendors?page=1",
      lastPageUrl: "/admin/delivery-management/vendors?page=1",
      nextPageUrl: null,
      previousPageUrl: null,
    },
    sum: {},
  };

  const updateVendorStatus = async (data: FormData) => {
    "use server";

    const vendorId = data.get("vendorId") as string;
    const status = data.get("status") as string;

    try {
      await api.patch(`admin/delivery-management/vendors/${vendorId}/status`, {
        verification_status: status,
      });
      revalidatePath("/admin/delivery-management/vendors");
    } catch (error) {
      console.error("Failed to update vendor status:", error);
      throw error;
    }
  };

  const suspendVendor = async (data: FormData) => {
    "use server";

    const vendorId = data.get("vendorId") as string;

    try {
      await api.patch(`admin/delivery-management/vendors/${vendorId}/suspend`);
      revalidatePath("/admin/delivery-management/vendors");
    } catch (error) {
      console.error("Failed to suspend vendor:", error);
      throw error;
    }
  };

  const bulkUpdateVendors = async (data: FormData) => {
    "use server";

    const vendorIds = JSON.parse(data.get("vendorIds") as string);
    const action = data.get("action") as string;

    try {
      await api.post(`admin/delivery-management/vendors/bulk-action`, {
        vendor_ids: vendorIds,
        action,
      });
      revalidatePath("/admin/delivery-management/vendors");
    } catch (error) {
      console.error("Failed to perform bulk action:", error);
      throw error;
    }
  };

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Delivery Vendors</h1>
        <p className="text-gray-600 mt-1">
          Manage delivery vendors, verification status, and performance metrics
        </p>
      </div>

      <AdminDeliveryVendorsTable
        data={vendors?.data || demoVendors.data}
        meta={vendors?.meta || demoVendors.meta}
        updateVendorStatus={updateVendorStatus}
        suspendVendor={suspendVendor}
        bulkUpdateVendors={bulkUpdateVendors}
      />
    </div>
  );
}
