"use client";

import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { imagePath } from "@/lib/api";
import { Chip, User } from "@nextui-org/react";
import { format as formatDate } from "date-fns";
import Link from "next/link";
import toast from "react-hot-toast";
import { PaginationMeta } from "@/types/pagination";
import { Button } from "@/components/ui/button";
import { useTransition } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";

interface TempOrder {
  id: string;
  customer?: {
    id: string;
    name?: string;
    email?: string;
    avatar?: {
      url?: string;
    };
    avatarUrl?: string;
  };
  total: number;
  type: 'Preorder' | 'Instant';
  delivery: 'Takeaway' | 'Dinein' | 'Delivery' | 'Selfpick';
  status: 'Pending' | 'Placed' | 'Processing' | 'Ready' | 'Delivering' | 'Delivered' | 'Completed' | 'Cancelled' | 'Rejected';
  createdAt: string;
}

export default function TempOrdersTable({
  data,
  meta,
  deleteOrder,
}: {
  data: TempOrder[];
  meta?: PaginationMeta;
  deleteOrder: (data: FormData) => Promise<void>;
}) {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const { data: session } = useSession();

  const handleDelete = async (id: string) => {
    try {
      const formData = new FormData();
      formData.append("id", id);
      await deleteOrder(formData);
      toast.success("Temporary order deleted successfully");
      router.refresh();
    } catch (error) {
      console.error('Error deleting temporary order:', error);
      toast.error("Failed to delete temporary order");
    }
  };

  return (
    <CustomTable
      title="Pending Requests"
      columns={[
        {
          name: "Customer",
          uid: "customer",
          renderCell: (order: TempOrder) => (
            <Link
              href={`/customers/${order.customer?.id}`}
              className="flex items-center space-x-2"
            >
              <User
                name={order.customer?.name}
                description={order.customer?.email}
                avatarProps={{
                  src: `${imagePath(order.customer?.avatar?.url, order.customer?.avatarUrl)}`,
                }}
              />
            </Link>
          ),
        },
        {
          name: "Amount",
          uid: "amount",
          renderCell: (order: TempOrder) => <>KES {order.total}</>,
        },
        {
          name: "Type",
          uid: "type",
          renderCell: (order: TempOrder) => (
            <Chip
              color={order.type === "Preorder" ? "primary" : "secondary"}
              variant="flat"
              className="text-xs"
            >
              {order.type}
            </Chip>
          ),
        },
        {
          name: "Delivery",
          uid: "delivery",
          renderCell: (order: TempOrder) => (
            <Chip
              color={
                order.delivery === "Takeaway"
                  ? "warning"
                  : order.delivery === "Dinein"
                    ? "success"
                    : order.delivery === "Delivery"
                      ? "primary"
                      : "default"
              }
              variant="flat"
              className="text-xs"
            >
              {order.delivery}
            </Chip>
          ),
        },
        {
          name: "Status",
          uid: "status",
          renderCell: (order: TempOrder) => (
            <Chip
              color={
                order.status === "Completed"
                  ? "success"
                  : order.status === "Pending"
                    ? "warning"
                    : order.status === "Placed"
                      ? "secondary"
                      : order.status === "Ready"
                        ? "primary"
                        : order.status === "Rejected"
                          ? "danger"
                          : "default"
              }
              variant="flat"
              className="text-xs"
            >
              {order.status}
            </Chip>
          ),
        },
        {
          name: "Date",
          uid: "createdAt",
          sortable: true,
          renderCell: (order: TempOrder) => (
            <>
              {formatDate(
                new Date(order.createdAt),
                "EEE, MMM dd, yyyy hh:mm a",
              )}
            </>
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (order: TempOrder) => (
            <div className="flex w-fit items-center gap-5">
              <Link href={`/temp-orders/${order.id}`}>
                <Icon
                  name="icon-[mingcute--eye-line]"
                  classNames="text-primary"
                />
              </Link>

              <Link href={`/temp-orders/${order.id}/edit`}>
                <Icon
                  name="icon-[mage--edit-pen-fill]"
                  classNames="text-primary"
                />
              </Link>

              {session?.user?.roles?.includes('admin' as any) && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleDelete(order.id)}
                  disabled={isPending}
                >
                  Delete
                </Button>
              )}
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
      filter={[
        {
          column: "status",
          values: [
            { name: "Pending", value: "Pending" },
            { name: "Placed", value: "Placed" },
            { name: "Processing", value: "Processing" },
            { name: "Ready", value: "Ready" },
            { name: "Delivering", value: "Delivering" },
            { name: "Delivered", value: "Delivered" },
            { name: "Completed", value: "Completed" },
            { name: "Cancelled", value: "Cancelled" },
            { name: "Rejected", value: "Rejected" },
          ],
        },
        {
          column: "type",
          displayName: "Type",
          values: [
            { name: "Preorder", value: "Preorder" },
            { name: "Instant", value: "Instant" },
          ],
        },
        {
          column: "delivery",
          displayName: "Delivery",
          values: [
            { name: "Takeaway", value: "Takeaway" },
            { name: "Dine-in", value: "Dinein" },
            { name: "Delivery", value: "Delivery" },
            { name: "Self Pickup", value: "Selfpick" },
          ],
        },
      ]}
    />
  );
} 