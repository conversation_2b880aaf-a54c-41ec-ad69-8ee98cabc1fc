import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import BranchStaffCreateForm from "./form";
import { Metadata } from "next";
import { cache } from "react";
import Link from "next/link";
import { toast } from "react-hot-toast";

const fetchBranch = cache((branchId: string) =>
  api.get<Branch>(`/branches/${branchId}`),
);

export const metadata: Metadata = {
  title: "Create Staff",
  description: "Create Staff",
  keywords: ["staff"],
};

export default async function Page({
  params,
}: {
  params: {
    branchId: string;
  };
}) {
  const branch = await fetchBranch(params.branchId);

  const createStaff = async (formData: FormData) => {
    "use server";

    try {
      if (branch) {
        formData.append("branchId", params.branchId);
        formData.append("vendorId", branch.vendorId);
      }

      // Create staff in the backend
      await api.post(`branches/${params.branchId}/staff`, formData);

      // Optionally trigger revalidation if needed
      revalidatePath(`/branches/${params.branchId}/staff`);
      toast.success("Staff created successfully!");
    } catch (error) {
      console.error("Error creating staff:", error);
      toast.error("Failed to create staff.");
    }
  };

  return (
    <div className="p-4">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="text-2xl text-primary">
          {branch ? `Create Staff for ${branch.name}` : "Create Staff"}
        </h1>

        <Link
          href={`/branches/${params.branchId}/staff`}
          className="flex items-center space-x-2 text-default-500"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="h-6 w-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6.75 15.75 3 12m0 0 3.75-3.75M3 12h18"
            />
          </svg>
          <span>Back to Staff</span>
        </Link>
      </div>
      {branch && (
        <BranchStaffCreateForm storeStaff={createStaff} branch={branch} />
      )}
    </div>
  );
}
