"use client";

import { 
  ProductFulfillmentPayload, 
  archetypeMap, 
  relevantSettings, 
  typeDefaults,
  ProductArchetype 
} from "@/types/fulfillment-settings";
import { useEffect } from "react";
import { UseFormRegister, UseFormWatch, UseFormSetValue, FieldErrors } from "react-hook-form";

interface FulfillmentSettingsProps {
  productType: string;
  register: UseFormRegister<any>;
  watch: UseFormWatch<any>;
  setValue: UseFormSetValue<any>;
  errors: FieldErrors<any>;
}

export default function FulfillmentSettings({ 
  productType, 
  register, 
  watch, 
  setValue, 
  errors 
}: FulfillmentSettingsProps) {
  const fulfillmentSettings = watch("fulfillmentSettings");
  const archetype = archetypeMap[productType] as ProductArchetype;

  // Apply defaults when product type changes
  useEffect(() => {
    if (productType && typeDefaults[productType]) {
      const defaults = typeDefaults[productType];
      Object.entries(defaults).forEach(([key, value]) => {
        setValue(`fulfillmentSettings.${key}` as any, value);
      });
    }
  }, [productType, setValue]);

  if (!archetype) {
    return (
      <div className="p-4 text-center text-gray-500">
        Please select a product type in the Details tab first.
      </div>
    );
  }

  const getArchetypeIcon = (archetype: ProductArchetype) => {
    switch (archetype) {
      case "PHYSICAL": return "🏷️";
      case "DIGITAL": return "💻";
      case "SERVICE": return "🛠️";
      default: return "📦";
    }
  };

  const getArchetypeTitle = (archetype: ProductArchetype) => {
    switch (archetype) {
      case "PHYSICAL": return "Physical Product Configuration";
      case "DIGITAL": return "Digital Product Configuration";
      case "SERVICE": return "Service Configuration";
      default: return "Product Configuration";
    }
  };

  const renderPhysicalSettings = () => (
    <>
      <div className="border border-gray-200 rounded-lg p-4 mb-4">
        <h3 className="text-lg font-semibold mb-3">Delivery Options</h3>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.isDeliverable")}
              className="mr-2"
            />
            Can be delivered
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.isPickup")}
              className="mr-2"
            />
            Can be picked up
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.physicalConsumptionIsOnsite")}
              className="mr-2"
            />
            Consumed at vendor location
          </label>
        </div>
      </div>

      <div className="border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3">Order Management</h3>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.preorderAllowed")}
              className="mr-2"
            />
            Allow pre-orders
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.hasForm")}
              className="mr-2"
            />
            Require additional forms
          </label>
        </div>
      </div>
    </>
  );

  const renderDigitalSettings = () => (
    <>
      <div className="border border-gray-200 rounded-lg p-4 mb-4">
        <h3 className="text-lg font-semibold mb-3">Digital Delivery</h3>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.isDownloadable")}
              className="mr-2"
            />
            Can be downloaded
          </label>
          <div>
            <label className="block text-sm font-medium mb-2">Delivery Method</label>
            <select
              {...register("fulfillmentSettings.digitalDeliveryMethod")}
              className="w-full p-2 border border-gray-300 rounded-lg"
            >
              <option value="emailLink">Email Link</option>
              <option value="inAppAccess">In-App Access</option>
              <option value="licenseKey">License Key</option>
            </select>
          </div>
        </div>
      </div>

      <div className="border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3">Order Management</h3>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.preorderAllowed")}
              className="mr-2"
            />
            Allow pre-orders
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.isInformational")}
              className="mr-2"
            />
            Informational only (no payment)
          </label>
        </div>
      </div>
    </>
  );

  const renderServiceSettings = () => (
    <>
      <div className="border border-gray-200 rounded-lg p-4 mb-4">
        <h3 className="text-lg font-semibold mb-3">Service Delivery</h3>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.isSchedulable")}
              className="mr-2"
            />
            Can be scheduled
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.serviceIsOnsite")}
              className="mr-2"
            />
            Performed at customer location
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.serviceIsRemote")}
              className="mr-2"
            />
            Can be performed remotely
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.serviceIsDynamicQuery")}
              className="mr-2"
            />
            Requires dynamic pricing queries
          </label>
        </div>
      </div>

      <div className="border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3">Order Management</h3>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.scheduleAllowed")}
              className="mr-2"
            />
            Allow scheduling
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register("fulfillmentSettings.hasForm")}
              className="mr-2"
            />
            Require additional forms
          </label>
        </div>
      </div>
    </>
  );

  const renderSettings = () => {
    switch (archetype) {
      case "PHYSICAL":
        return renderPhysicalSettings();
      case "DIGITAL":
        return renderDigitalSettings();
      case "SERVICE":
        return renderServiceSettings();
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center mb-6">
        <span className="text-2xl mr-3">{getArchetypeIcon(archetype)}</span>
        <h2 className="text-xl font-bold">{getArchetypeTitle(archetype)}</h2>
      </div>

      {renderSettings()}
    </div>
  );
} 