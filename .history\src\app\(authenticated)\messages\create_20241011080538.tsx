"use client";

import AsyncSelect from "react-select/async";
import { useState } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { useClickOutside } from "@/hooks/useClickOutside";
import { useSession } from "next-auth/react";

export default function AdminMessagesCreateForm({
  storeMessage,
}: {
  storeMessage: (data: FormData) => Promise<void>;
}) {
  const [creating, setCreating] = useState(false);
  const [preview, setPreview] = useState<string>();

  const messageRef = useClickOutside(() => setCreating(false));
  const { data: session } = useSession();

  const {
    setValue,
    watch,
    handleSubmit,
    register,
    reset,
    control,
    formState: { errors, isSubmitting },
  } = useForm<Message & { upload: File; channels: string[]; title: string; type: string }>({
    defaultValues: {
      type: "",   // This will store the broadcast type
      templateId: "",
      channels: [],
      message: "",
      branchId: session?.branch?.id,
      vendorId: session?.vendor?.id,
      userId: session?.id,
    },
  });

  const broadcast = watch(); // Watch the form data changes

  // Load message templates asynchronously
  const loadTemplates = async (inputValue: string) => {
    const response = await fetch(`/api/messages/templates?s=${inputValue}`);
    const data = await response.json();
    return data.map((template) => ({
      label: template.name,
      value: template.id,
      content: template.content,
    }));
  };

  const sendMessage = async (data) => {
    try {
      // Send message logic using the form data
      const formData = new FormData();
      formData.append("type", data.type); // Broadcast type
      formData.append("templateId", data.templateId); // Message template
      formData.append("message", data.message); // Message content
      formData.append("title", data.title);

      data.channels.forEach((channel, i) => {
        formData.append(`channels[${i}]`, channel); // Selected channels
      });

      await storeMessage(formData);
      toast.success("Message sent successfully!");
      reset();
      setCreating(false);
    } catch (error) {
      toast.error("Error sending message.");
    }
  };

  return (
    <>
      <button
        onClick={() => setCreating(true)}
        id="createMessageButton"
        className="flex items-center justify-center rounded-lg bg-primary px-6 py-2 text-white hover:bg-default-800 dark:bg-primary"
        type="button"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          className="bi bi-plus-lg h-5 w-5"
          viewBox="0 0 16 16"
        >
          <path
            fillRule="evenodd"
            d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2Z"
          />
        </svg>
        <span className="ml-2">New Message</span>
      </button>

      {creating && (
        <div
          ref={messageRef}
          id="drawer-create-message-default"
          className="fixed right-0 top-0 z-40 h-screen w-full max-w-xs overflow-y-auto bg-white p-4 shadow-sm transition-transform dark:bg-default-800"
          tabIndex={-1}
          aria-labelledby="drawer-label"
          aria-hidden="true"
        >
          <h5
            id="drawer-label"
            className="mb-6 inline-flex items-center text-sm font-semibold uppercase text-default-500 dark:text-default-400"
          >
            New Message
          </h5>
          <button
            onClick={() => setCreating(false)}
            type="button"
            className="absolute right-2.5 top-2.5 inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-default-400 hover:bg-default-200 hover:text-default-900 dark:hover:bg-default-600 dark:hover:text-white"
          >
            <svg
              aria-hidden="true"
              className="h-5 w-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span className="sr-only">Close menu</span>
          </button>
          <form onSubmit={handleSubmit(sendMessage)}>
            <div className="space-y-4">

              {/* Broadcast Type */}
              <div>
                <label>Select broadcast type</label>
                <Controller
                  name="type"
                  control={control}
                  render={({ field }) => (
                    <AsyncSelect
                      {...field}
                      options={[
                        { value: "all", label: "All customers" },
                        { value: "groups", label: "Customer groups" },
                        { value: "individual", label: "Individual customers" },
                      ]}
                      onChange={(option) => setValue("type", option.value)}
                      placeholder="Select broadcast type"
                    />
                  )}
                />
              </div>

              {/* Message Template */}
              <div>
                <label>Select a message template</label>
                <Controller
                  name="templateId"
                  control={control}
                  render={({ field }) => (
                    <AsyncSelect
                      {...field}
                      loadOptions={loadTemplates}
                      onChange={(option) => {
                        setValue("templateId", option.value);
                        setValue("message", option.content);
                      }}
                      placeholder="Message template"
                    />
                  )}
                />
              </div>

              {/* Message Content */}
              {broadcast.message && (
                <div>
                  <label>Message</label>
                  <textarea
                    {...register("message")}
                    value={broadcast.message}
                    readOnly
                    className="w-full p-2 border rounded-lg"
                  />
                </div>
              )}

              {/* Channels */}
              <div>
                <label>Select channel(s)</label>
                <Controller
                  name="channels"
                  control={control}
                  render={({ field }) => (
                    <AsyncSelect
                      {...field}
                      isMulti
                      options={[
                        { value: "in-app", label: "In App" },
                        { value: "sms", label: "SMS" },
                        { value: "email", label: "Email" },
                      ]}
                      onChange={(options) =>
                        setValue(
                          "channels",
                          options.map((option) => option.value)
                        )
                      }
                      placeholder="Select channels"
                    />
                  )}
                />
              </div>

              {/* Submit Button */}
              <div className="bottom-0 left-0 flex w-full justify-center space-x-4 pb-4 md:absolute md:px-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                >
                  Send message
                </button>
              </div>
            </div>
          </form>
        </div>
      )}
    </>
  );
}
