import { auth } from "@/auth";
import PaginatedTable from "@/components/table";
import CustomersTable from "@/components/tables/customers-table";
import { api, imagePath } from "@/lib/api";
import { PlusIcon } from "@radix-ui/react-icons";
import { formatDistance } from "date-fns";
import { Metadata } from "next";
import { revalidatePath } from "next/cache";
import Image from "next/image";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Customers",
};

export default async function page({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {

  const session = await auth(); // Assuming auth retrieves the session

  // Check if the branch is available
  if (!session?.branch) {
    const customers = await api.get<PaginatedData<User>>(`/customers`, {
      ...searchParams,
      role: "customer",
      per: 20,
    });
  
    return null;
  }  

  const customers = await api.get<PaginatedData<User>>(`/branches/${session?.branch?.id}/customers`, {
    ...searchParams,
    role: "customer",
    per: 20,
  });

  // https://aia-staging.onrender.com/v1/branches/01hr4m3xxkvna4fgr7n00r24j5/customers

  // Check the role of the logged in user

  // // Safely access roles
  // const userRoles = session?.user?.roles ?? []; // Default to an empty array if roles are undefined
  // const userDetailRole = userRoles.length >= 0 ? userRoles[0]?.name : "Unknown";

  // console.log(`These are the user details in the Customer Page: ${userDetailRole}`);

  // branch id
  const branchId = session?.branch?.id;
  const branchName = session?.branch?.name;
  console.log(`The branch ID is: ${branchId}`);

  // Define the deleteCustomer function
  const deleteCustomer = async (data: FormData) => {
    "use server";

    const response = await api.destroy(String(data.get("id")));

    revalidatePath("/customers");
  };

  return (
    <div className="page-content p-10">
      {/* Button to create new customer */}
      <div className="flex justify-end mb-4">
        <Link
          className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
          data-menu-key="products-add"
          href="/customers/create"
        >
          <svg
            stroke="currentColor"
            fill="none"
            strokeWidth="2"
            viewBox="0 0 24 24"
            strokeLinecap="round"
            strokeLinejoin="round"
            height="24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="12.1" cy="12.1" r="1"></circle>
          </svg>
          Add Customer
        </Link>
      </div>

      {customers && (
        <CustomersTable
          data={customers?.data}
          meta={customers.meta}
          action={deleteCustomer}
        />
      )}
    </div>
  );
}
