import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect, notFound } from "next/navigation";
import { ServiceArea } from "@/types";
import { Card, CardBody, CardHeader, Chip, Button } from "@nextui-org/react";
import { ArrowLeft, Edit, MapPin } from "lucide-react";
import Link from "next/link";
import { Icon } from "@/components/icon";

const fetchServiceArea = cache(
  (vendorId: string, serviceAreaId: string) =>
    api.get<ServiceArea>(`vendors/${vendorId}/service-areas/${serviceAreaId}`),
);

export const generateMetadata = async ({
  params,
}: {
  params: { serviceAreaId: string };
}) => {
  return {
    title: "Service Area Details",
    description: "View service area details and coverage information",
    keywords: ["service area", "delivery", "coverage"],
  };
};

export default async function ServiceAreaDetailPage({
  params: { serviceAreaId },
}: {
  params: { serviceAreaId: string };
}) {
  const session = await auth();

  // Check if user is authenticated and has vendor access
  if (!session) {
    redirect('/login');
  }

  // Get vendor ID from session
  const vendorId = session?.vendor?.id || session?.branch?.vendorId;
  
  if (!vendorId) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            No Vendor Access
          </h2>
          <p className="text-gray-600">
            You need to be associated with a vendor to view service areas.
          </p>
        </div>
      </div>
    );
  }

  const serviceArea = await fetchServiceArea(vendorId, serviceAreaId);

  if (!serviceArea) {
    notFound();
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'circle':
        return <Icon name="icon-[mingcute--location-line]" classNames="text-blue-500" />;
      case 'polygon':
        return <Icon name="icon-[mingcute--polygon-line]" classNames="text-green-500" />;
      case 'administrative':
        return <Icon name="icon-[mingcute--building-2-line]" classNames="text-purple-500" />;
      default:
        return <Icon name="icon-[mingcute--map-pin-line]" classNames="text-gray-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'circle':
        return 'primary';
      case 'polygon':
        return 'success';
      case 'administrative':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const formatCoverage = (serviceArea: ServiceArea) => {
    switch (serviceArea.type) {
      case 'circle':
        return `${serviceArea.radius}km radius from branch location`;
      case 'polygon':
        return 'Custom polygon boundary';
      case 'administrative':
        return serviceArea.administrative_areas?.length 
          ? `${serviceArea.administrative_areas.length} administrative area(s)`
          : 'Administrative areas';
      default:
        return 'Unknown coverage type';
    }
  };

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <Button
          as={Link}
          href="/service-areas"
          variant="light"
          startContent={<ArrowLeft className="size-4" />}
          className="mb-4"
        >
          Back to Service Areas
        </Button>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getTypeIcon(serviceArea.type)}
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{serviceArea.name}</h1>
              {serviceArea.description && (
                <p className="text-gray-600 mt-1">{serviceArea.description}</p>
              )}
            </div>
          </div>
          <Button
            as={Link}
            href={`/service-areas/${serviceAreaId}/edit`}
            color="primary"
            startContent={<Edit className="size-4" />}
          >
            Edit
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Basic Information</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Type</span>
              <Chip
                size="sm"
                variant="flat"
                color={getTypeColor(serviceArea.type) as any}
                className="capitalize"
                startContent={getTypeIcon(serviceArea.type)}
              >
                {serviceArea.type}
              </Chip>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-600">Status</span>
              <Chip
                size="sm"
                color={serviceArea.active ? "success" : "danger"}
                variant="flat"
              >
                {serviceArea.active ? "Active" : "Inactive"}
              </Chip>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-600">Priority</span>
              <Chip size="sm" variant="bordered">
                {serviceArea.priority}
              </Chip>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-600">Branch</span>
              <span className="font-medium">
                {serviceArea.branch?.name || 'No branch assigned'}
              </span>
            </div>

            <div className="flex justify-between items-start">
              <span className="text-gray-600">Coverage</span>
              <span className="font-medium text-right">
                {formatCoverage(serviceArea)}
              </span>
            </div>
          </CardBody>
        </Card>

        {/* Delivery Settings */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Delivery Settings</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            {serviceArea.meta?.estimated_delivery_time && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Estimated Delivery Time</span>
                <span className="font-medium">
                  {serviceArea.meta.estimated_delivery_time} minutes
                </span>
              </div>
            )}

            {serviceArea.meta?.delivery_fee !== undefined && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Delivery Fee</span>
                <span className="font-medium">
                  KES {serviceArea.meta.delivery_fee.toFixed(2)}
                </span>
              </div>
            )}

            {serviceArea.meta?.special_instructions && (
              <div>
                <span className="text-gray-600 block mb-2">Special Instructions</span>
                <p className="text-sm bg-gray-50 p-3 rounded-lg">
                  {serviceArea.meta.special_instructions}
                </p>
              </div>
            )}
          </CardBody>
        </Card>

        {/* Coverage Map Placeholder */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <h3 className="text-lg font-semibold flex items-center space-x-2">
              <MapPin className="size-5" />
              <span>Coverage Area</span>
            </h3>
          </CardHeader>
          <CardBody>
            <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <MapPin className="size-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600">Interactive map coming soon</p>
                <p className="text-sm text-gray-500">
                  This will show the coverage area on an interactive map
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Metadata */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <h3 className="text-lg font-semibold">System Information</h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Created</span>
                <span>{new Date(serviceArea.created_at).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Last Updated</span>
                <span>{new Date(serviceArea.updated_at).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Service Area ID</span>
                <span className="font-mono text-xs">{serviceArea.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Vendor ID</span>
                <span className="font-mono text-xs">{serviceArea.vendor_id}</span>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
