"use client";

import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function ProcessRequest({
  defaultValues,
  updateRequest,
}: {
  defaultValues: TempOrder;
  updateRequest: (data: TempOrder) => Promise<TempOrder | undefined>;
}) {
  const router = useRouter();

  const { register, handleSubmit, setValue } = useForm({
    defaultValues,
  });

  const onChange = (data: TempOrder) => {
    toast
      .promise(updateRequest(data), {
        loading: "Updating request...",
        success: "Request updated",
        error: "Error updating request",
      })
      .then((order) => {
        if (order) {
          router.push(`/orders/${order.id}`);
        }
      });
  };

  return (
    <form className="grid grid-cols-2 gap-5" onSubmit={handleSubmit(onChange)}>
      {[
        "Pending",
        "Placed",
        "Processing",
        "Ready",
        "Delivering",
        "Delivered",
        "Completed",
        "Cancelled",
        "Rejected",
      ].map((status) => (
        <button
          key={status}
          type="submit"
          value={status}
          {...register("status")}
          className={
            "flex items-center justify-center rounded-lg px-4 py-2 font-bold " +
            (status === defaultValues.status
              ? "bg-primary text-white"
              : "border border-primary text-primary")
          }
          onClick={() => setValue("status", status)}
        >
          {status}
        </button>
      ))}
    </form>
  );
}
