import { Metadata } from "next";
import { Suspense } from "react";
import { Spinner } from "@nextui-org/react";
import PackagingOptionsTable from "@/components/tables/packaging-options-table";
import { getPackagingOptions } from "@/actions/packaging-options";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata: Metadata = {
  title: "Packaging Options",
  description: "Manage your product packaging options",
};

interface PageProps {
  searchParams?: Record<string, string>;
}

async function PackagingOptionsContent({ searchParams }: PageProps) {
  let packagingOptions;
  let error = null;

  try {
    packagingOptions = await getPackagingOptions(searchParams);
  } catch (err) {
    console.error("Error loading packaging options:", err);
    error = "Failed to load packaging options. Please try refreshing the page or contact support if the problem persists.";
  }

  return (
    <PackagingOptionsTable
      data={packagingOptions?.data || []}
      meta={packagingOptions?.meta || {
        total: 0,
        perPage: 25,
        currentPage: 1,
        lastPage: 1,
        firstPage: 1,
        firstPageUrl: "/products/packaging-options?page=1",
        lastPageUrl: "/products/packaging-options?page=1",
        nextPageUrl: null,
        previousPageUrl: null
      }}
      error={error}
    />
  );
}

export default function PackagingOptionsPage({ searchParams }: PageProps) {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-default-900">Packaging Options</h1>
        <p className="text-default-600 mt-2">
          Create and manage reusable packaging options that can be associated with your products.
          Packaging charges are automatically applied to Takeaway, Delivery, and Self-pickup orders.
        </p>
      </div>

      <Suspense 
        fallback={
          <div className="flex items-center justify-center min-h-[400px]">
            <Spinner size="lg" color="primary" />
          </div>
        }
      >
        <PackagingOptionsContent searchParams={searchParams} />
      </Suspense>
    </div>
  );
}
