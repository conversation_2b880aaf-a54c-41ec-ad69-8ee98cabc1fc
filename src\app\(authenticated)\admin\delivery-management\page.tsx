import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect } from "next/navigation";
import AdminDeliveryDashboard from "@/components/admin/delivery/admin-delivery-dashboard";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface DeliveryMetrics {
  active_vendors: number;
  pending_verifications: number;
  total_service_areas: number;
  coverage_regions: number;
  total_deliveries_today: number;
  success_rate: number;
  average_delivery_time: number;
  revenue_today: number;
}

interface RecentActivity {
  id: string;
  type: 'vendor_verified' | 'service_area_created' | 'verification_pending' | 'coverage_conflict';
  message: string;
  timestamp: string;
  vendor?: {
    id: string;
    name: string;
  };
}

const fetchDeliveryMetrics = cache(
  () => api.get<DeliveryMetrics>('admin/delivery-management/metrics'),
);

const fetchRecentActivity = cache(
  () => api.get<RecentActivity[]>('admin/delivery-management/activity'),
);

export const generateMetadata = async () => {
  return {
    title: "Delivery Management Dashboard",
    description: "Admin dashboard for delivery management system oversight",
    keywords: ["admin", "delivery", "management", "dashboard"],
  };
};

export default async function AdminDeliveryManagementPage() {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  // Fetch dashboard data
  const [metrics, recentActivity] = await Promise.all([
    fetchDeliveryMetrics().catch(() => null),
    fetchRecentActivity().catch(() => []),
  ]);

  // Fallback demo data if API is not available
  const demoMetrics: DeliveryMetrics = {
    active_vendors: 247,
    pending_verifications: 12,
    total_service_areas: 1834,
    coverage_regions: 156,
    total_deliveries_today: 1250,
    success_rate: 94.5,
    average_delivery_time: 28,
    revenue_today: 125000,
  };

  const demoActivity: RecentActivity[] = [
    {
      id: "1",
      type: "vendor_verified",
      message: "FastDelivery Co. has been verified and approved",
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
      vendor: { id: "v1", name: "FastDelivery Co." },
    },
    {
      id: "2",
      type: "service_area_created",
      message: "New service area created in Westlands region",
      timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
      vendor: { id: "v2", name: "QuickCourier" },
    },
    {
      id: "3",
      type: "verification_pending",
      message: "SpeedyService submitted verification documents",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
      vendor: { id: "v3", name: "SpeedyService" },
    },
    {
      id: "4",
      type: "coverage_conflict",
      message: "Overlapping service areas detected in CBD region",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
    },
  ];

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Delivery Management Dashboard</h1>
        <p className="text-gray-600 mt-1">
          System-wide overview and management of delivery operations
        </p>
      </div>

      <AdminDeliveryDashboard
        metrics={metrics || demoMetrics}
        recentActivity={recentActivity || demoActivity}
      />
    </div>
  );
}
