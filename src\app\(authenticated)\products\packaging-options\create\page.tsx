import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { <PERSON><PERSON> } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import PackagingOptionForm from "@/components/forms/packaging-option-form";

export const metadata: Metadata = {
  title: "Create Packaging Option",
  description: "Create a new packaging option for your products",
};

export default function CreatePackagingOptionPage() {
  return (
    <div className="min-h-screen bg-default-50">
      {/* Header */}
      <div className="bg-white border-b border-default-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center gap-4">
            <Button
              as={Link}
              href="/products/packaging-options"
              variant="light"
              isIconOnly
              className="text-default-600"
            >
              <Icon name="icon-[heroicons--arrow-left-20-solid]" classNames="h-5 w-5" />
            </Button>
            
            <div>
              <h1 className="text-2xl font-bold text-default-900">Create Packaging Option</h1>
              <p className="text-default-600">
                Add a new packaging option that can be associated with your products
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="py-8">
        <PackagingOptionForm mode="create" />
      </div>
    </div>
  );
}
