import { Metada<PERSON> } from "next";
import Link from "next/link";
import { notFound } from "next/navigation";
import { Button } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import PackagingOptionForm from "@/components/forms/packaging-option-form";
import { getPackagingOption } from "@/actions/packaging-options";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface PageProps {
  params: {
    optionId: string;
  };
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  try {
    const packagingOption = await getPackagingOption(params.optionId);
    
    return {
      title: `Edit ${packagingOption?.name || "Packaging Option"}`,
      description: `Edit packaging option: ${packagingOption?.name || "Unknown"}`,
    };
  } catch (error) {
    return {
      title: "Edit Packaging Option",
      description: "Edit packaging option",
    };
  }
}

export default async function EditPackagingOptionPage({ params }: PageProps) {
  let packagingOption;
  let error = null;

  try {
    packagingOption = await getPackagingOption(params.optionId);

    if (!packagingOption) {
      notFound();
    }
  } catch (err) {
    console.error("Error loading packaging option:", err);
    error = "Failed to load packaging option details. Some features may not work correctly.";
    // Don't call notFound() - let the page render with error state
  }

  return (
    <div className="min-h-screen bg-default-50">
      {/* Header */}
      <div className="bg-white border-b border-default-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center gap-4">
            <Button
              as={Link}
              href="/products/packaging-options"
              variant="light"
              isIconOnly
              className="text-default-600"
            >
              <Icon name="icon-[heroicons--arrow-left-20-solid]" classNames="h-5 w-5" />
            </Button>
            
            <div>
              <h1 className="text-2xl font-bold text-default-900">
                Edit Packaging Option
              </h1>
              <p className="text-default-600">
                {packagingOption
                  ? `Update the details for "${packagingOption.name}"`
                  : "Update packaging option details"
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <div className="max-w-2xl mx-auto px-6 py-4">
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <Icon name="icon-[heroicons--exclamation-triangle-20-solid]" classNames="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-red-800 font-medium mb-1">Error loading packaging option</h3>
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Form Content */}
      <div className="py-8">
        {packagingOption ? (
          <PackagingOptionForm
            mode="edit"
            defaultValues={packagingOption}
          />
        ) : (
          <div className="max-w-2xl mx-auto p-6">
            <div className="text-center py-12">
              <Icon name="icon-[heroicons--exclamation-triangle-20-solid]" classNames="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-default-900 mb-2">Unable to load packaging option</h3>
              <p className="text-default-600 mb-6">
                The packaging option could not be loaded. It may have been deleted or you may not have permission to access it.
              </p>
              <div className="flex items-center justify-center gap-3">
                <Button
                  as={Link}
                  href="/products/packaging-options"
                  variant="light"
                >
                  Back to Packaging Options
                </Button>
                <Button
                  color="primary"
                  onPress={() => window.location.reload()}
                  startContent={<Icon name="icon-[heroicons--arrow-path-20-solid]" classNames="h-4 w-4" />}
                >
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
