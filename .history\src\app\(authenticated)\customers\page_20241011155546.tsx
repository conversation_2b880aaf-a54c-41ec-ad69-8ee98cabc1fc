import { auth } from "@/auth";
import CustomersTable from "@/components/tables/customers-table";
import { api } from "@/lib/api";
import { Metadata } from "next";
import { revalidatePath } from "next/cache";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Customers",
};

export default async function page({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth(); // Retrieve the session
  let customers;

  // Check if the user has a branch assigned, otherwise fetch all customers
  if (!session?.branch) {
    // Fetch all customers if no branch (admin user)
    customers = await api.get<PaginatedData<User>>(`/customers`, {
      ...searchParams,
      role: "customer",
      per: 20,
    });
  } else {
    // Fetch customers associated with the specific branch
    customers = await api.get<PaginatedData<User>>(
      `/branches/${session.branch.id}/customers`,
      {
        ...searchParams,
        role: "customer",
        per: 20,
      }
    );
  }

  // Define the deleteCustomer function
  const deleteCustomer = async (data: FormData) => {
    "use server";

    const response = await api.destroy(String(data.get("id")));
    revalidatePath("/customers");
  };

  return (
    <div className="page-content p-10">
      {/* Button to create new customer */}
      <div className="flex justify-end mb-4">
        <Link
          className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
          data-menu-key="products-add"
          href="/customers/create"
        >
          <svg
            stroke="currentColor"
            fill="none"
            strokeWidth="2"
            viewBox="0 0 24 24"
            strokeLinecap="round"
            strokeLinejoin="round"
            height="24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="12.1" cy="12.1" r="1"></circle>
          </svg>
          Add Customer
        </Link>
      </div>

      {/* Display the CustomersTable if customers data is available */}
      {customers && (
        <CustomersTable
          data={customers.data}
          meta={customers.meta}
          action={deleteCustomer}
        />
      )}
    </div>
  );
}
