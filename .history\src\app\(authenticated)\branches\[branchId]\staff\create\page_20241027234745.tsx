import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import BranchStaffCreateForm from "./form";
import { Metadata } from "next";
import { cache } from "react";
import Link from "next/link";


const fetchBranch = cache((branchId: string) =>
  api.get<Branch>(`/branches/${branchId}`),
);

export const metadata: Metadata = {
  title: "Create Staff",
  description: "Create Staff",
  keywords: ["staff"],
};

export default async function page({
  params,
}: {
  params: {
    branchId: string;
  };
}) {
  const branch = await fetchBranch(params.branchId);

  const createStaff = async (formData: FormData) => {
    "use server";

    if (branch) {
      formData.append("branchId", params.branchId);
      formData.append("vendorId", branch.vendorId);
    }

    await api.post(`branches/${params.branchId}/staff`, formData);
  };


  const addRoleToUser = async (
    userId: string,
    role: string,
    firstName: string,
    lastName: string,
    phone: string,
    email: string,
    password: string = "`1234567890-=QAz" // default password
  ) => {
    try {
      await api.post(`auth/addrole/${userId}`, {
        firstName,
        lastName,
        phone,
        email,
        password,
        role,
      });
      
      toast.success("Role assigned successfully.");
    } catch (error) {
      console.error("Error assigning role:", error);
      toast.error("Could not assign role.");
    }
  };

  
  return (
    <div className="p-4">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="text-2xl text-primary">
          {branch ? `Create Staff for ${branch.name}` : "Create Staff"}
        </h1>

        <Link
          href={`/branches/${params.branchId}/staff`}
          className="flex items-center space-x-2 text-default-500"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="h-6 w-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6.75 15.75 3 12m0 0 3.75-3.75M3 12h18"
            />
          </svg>

          <span>Back to Staff</span>
        </Link>
      </div>
      {branch && (
        <BranchStaffCreateForm storeStaff={createStaff} branch={branch} />
      )}
    </div>
  );
}
