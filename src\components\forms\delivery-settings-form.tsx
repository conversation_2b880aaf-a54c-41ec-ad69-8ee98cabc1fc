"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CardHeader,
  Input,
  Select,
  SelectItem,
  Switch,
  Button,
  Tabs,
  Tab,
  Chip,
  Divider,
} from "@nextui-org/react";
import toast from "react-hot-toast";

interface DeliverySettings {
  delivery_preferences?: {
    max_distance?: number;
    preferred_vehicle_types?: string[];
    working_hours?: {
      start: string;
      end: string;
      days: number[];
    };
    auto_accept_orders?: boolean;
    max_orders_per_day?: number;
  };
  availability_settings?: {
    is_available?: boolean;
    current_status?: string;
    break_times?: Array<{
      start: string;
      end: string;
    }>;
    unavailable_dates?: string[];
  };
  pricing_structure?: {
    base_price?: number;
    price_per_km?: number;
    minimum_order_value?: number;
    surge_pricing?: {
      enabled: boolean;
      multiplier: number;
      conditions: string[];
    };
    discounts?: Array<{
      type: string;
      value: number;
      conditions: string[];
    }>;
  };
  verification_status?: string;
}

interface DeliverySettingsFormProps {
  deliverySettings?: DeliverySettings;
  vendorId: string;
  updatePreferences: (data: FormData) => Promise<void>;
  updateAvailability: (data: FormData) => Promise<void>;
  updatePricing: (data: FormData) => Promise<void>;
}

export default function DeliverySettingsForm({
  deliverySettings,
  vendorId,
  updatePreferences,
  updateAvailability,
  updatePricing,
}: DeliverySettingsFormProps) {
  const [activeTab, setActiveTab] = useState("preferences");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Vehicle type options
  const vehicleTypes = [
    { key: "motorcycle", label: "Motorcycle" },
    { key: "bicycle", label: "Bicycle" },
    { key: "car", label: "Car" },
    { key: "van", label: "Van" },
    { key: "truck", label: "Truck" },
  ];

  // Day options for working hours
  const dayOptions = [
    { key: 0, label: "Sunday" },
    { key: 1, label: "Monday" },
    { key: 2, label: "Tuesday" },
    { key: 3, label: "Wednesday" },
    { key: 4, label: "Thursday" },
    { key: 5, label: "Friday" },
    { key: 6, label: "Saturday" },
  ];

  const {
    register: registerPreferences,
    handleSubmit: handleSubmitPreferences,
    formState: { errors: preferencesErrors },
  } = useForm({
    defaultValues: {
      max_distance: deliverySettings?.delivery_preferences?.max_distance || 15,
      preferred_vehicle_types: deliverySettings?.delivery_preferences?.preferred_vehicle_types || [],
      working_hours_start: deliverySettings?.delivery_preferences?.working_hours?.start || "08:00",
      working_hours_end: deliverySettings?.delivery_preferences?.working_hours?.end || "22:00",
      working_days: deliverySettings?.delivery_preferences?.working_hours?.days || [1, 2, 3, 4, 5, 6],
      auto_accept_orders: deliverySettings?.delivery_preferences?.auto_accept_orders || false,
      max_orders_per_day: deliverySettings?.delivery_preferences?.max_orders_per_day || 50,
    },
  });

  const {
    register: registerAvailability,
    handleSubmit: handleSubmitAvailability,
    formState: { errors: availabilityErrors },
  } = useForm({
    defaultValues: {
      is_available: deliverySettings?.availability_settings?.is_available ?? true,
      current_status: deliverySettings?.availability_settings?.current_status || "online",
    },
  });

  const {
    register: registerPricing,
    handleSubmit: handleSubmitPricing,
    formState: { errors: pricingErrors },
  } = useForm({
    defaultValues: {
      base_price: deliverySettings?.pricing_structure?.base_price || 100,
      price_per_km: deliverySettings?.pricing_structure?.price_per_km || 20,
      minimum_order_value: deliverySettings?.pricing_structure?.minimum_order_value || 500,
      surge_enabled: deliverySettings?.pricing_structure?.surge_pricing?.enabled || false,
      surge_multiplier: deliverySettings?.pricing_structure?.surge_pricing?.multiplier || 1.5,
    },
  });

  const onSubmitPreferences = async (data: any) => {
    setIsSubmitting(true);
    
    try {
      const formData = new FormData();
      
      const preferences = {
        max_distance: data.max_distance,
        preferred_vehicle_types: data.preferred_vehicle_types,
        working_hours: {
          start: data.working_hours_start,
          end: data.working_hours_end,
          days: data.working_days,
        },
        auto_accept_orders: data.auto_accept_orders,
        max_orders_per_day: data.max_orders_per_day,
      };
      
      formData.append('preferences', JSON.stringify(preferences));
      await updatePreferences(formData);
      
      toast.success('Delivery preferences updated successfully!');
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('Failed to update delivery preferences');
    } finally {
      setIsSubmitting(false);
    }
  };

  const onSubmitAvailability = async (data: any) => {
    setIsSubmitting(true);
    
    try {
      const formData = new FormData();
      
      const settings = {
        is_available: data.is_available,
        current_status: data.current_status,
        break_times: [],
        unavailable_dates: [],
      };
      
      formData.append('settings', JSON.stringify(settings));
      await updateAvailability(formData);
      
      toast.success('Availability settings updated successfully!');
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('Failed to update availability settings');
    } finally {
      setIsSubmitting(false);
    }
  };

  const onSubmitPricing = async (data: any) => {
    setIsSubmitting(true);
    
    try {
      const formData = new FormData();
      
      const pricing = {
        base_price: data.base_price,
        price_per_km: data.price_per_km,
        minimum_order_value: data.minimum_order_value,
        surge_pricing: {
          enabled: data.surge_enabled,
          multiplier: data.surge_multiplier,
          conditions: ["peak_hours", "bad_weather"],
        },
        discounts: [],
      };
      
      formData.append('pricing', JSON.stringify(pricing));
      await updatePricing(formData);
      
      toast.success('Pricing structure updated successfully!');
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('Failed to update pricing structure');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl">
      <Tabs
        selectedKey={activeTab}
        onSelectionChange={(key) => setActiveTab(key as string)}
        className="w-full"
        variant="underlined"
      >
        <Tab key="preferences" title="Preferences">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Delivery Preferences</h3>
            </CardHeader>
            <CardBody>
              <form onSubmit={handleSubmitPreferences(onSubmitPreferences)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    type="number"
                    label="Maximum Delivery Distance (km)"
                    placeholder="15"
                    min={1}
                    max={100}
                    {...registerPreferences('max_distance', { 
                      required: 'Maximum distance is required',
                      min: { value: 1, message: 'Distance must be at least 1 km' },
                      max: { value: 100, message: 'Distance must be 100 km or less' }
                    })}
                    errorMessage={preferencesErrors.max_distance?.message}
                    isInvalid={!!preferencesErrors.max_distance}
                  />

                  <Input
                    type="number"
                    label="Max Orders Per Day"
                    placeholder="50"
                    min={1}
                    max={100}
                    {...registerPreferences('max_orders_per_day', { 
                      required: 'Max orders per day is required',
                      min: { value: 1, message: 'Must be at least 1 order' },
                      max: { value: 100, message: 'Must be 100 orders or less' }
                    })}
                    errorMessage={preferencesErrors.max_orders_per_day?.message}
                    isInvalid={!!preferencesErrors.max_orders_per_day}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    type="time"
                    label="Working Hours Start"
                    {...registerPreferences('working_hours_start', { 
                      required: 'Start time is required'
                    })}
                    errorMessage={preferencesErrors.working_hours_start?.message}
                    isInvalid={!!preferencesErrors.working_hours_start}
                  />

                  <Input
                    type="time"
                    label="Working Hours End"
                    {...registerPreferences('working_hours_end', { 
                      required: 'End time is required'
                    })}
                    errorMessage={preferencesErrors.working_hours_end?.message}
                    isInvalid={!!preferencesErrors.working_hours_end}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    {...registerPreferences('auto_accept_orders')}
                    defaultSelected={deliverySettings?.delivery_preferences?.auto_accept_orders || false}
                    color="success"
                  >
                    Auto-accept orders
                  </Switch>
                </div>

                <Divider />

                <div className="flex justify-end">
                  <Button
                    type="submit"
                    color="primary"
                    isLoading={isSubmitting}
                    isDisabled={isSubmitting}
                  >
                    Update Preferences
                  </Button>
                </div>
              </form>
            </CardBody>
          </Card>
        </Tab>

        <Tab key="availability" title="Availability">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Availability Settings</h3>
            </CardHeader>
            <CardBody>
              <form onSubmit={handleSubmitAvailability(onSubmitAvailability)} className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Current Availability</h4>
                    <p className="text-sm text-gray-600">Toggle your availability for new orders</p>
                  </div>
                  <Switch
                    {...registerAvailability('is_available')}
                    defaultSelected={deliverySettings?.availability_settings?.is_available ?? true}
                    color="success"
                    size="lg"
                  />
                </div>

                <Select
                  label="Current Status"
                  placeholder="Select your current status"
                  {...registerAvailability('current_status')}
                  defaultSelectedKeys={[deliverySettings?.availability_settings?.current_status || "online"]}
                >
                  <SelectItem key="online" value="online">
                    Online - Ready for orders
                  </SelectItem>
                  <SelectItem key="busy" value="busy">
                    Busy - Limited availability
                  </SelectItem>
                  <SelectItem key="offline" value="offline">
                    Offline - Not accepting orders
                  </SelectItem>
                </Select>

                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Coming Soon</h4>
                  <p className="text-sm text-blue-800">
                    Advanced availability features like break times and unavailable dates will be available soon.
                  </p>
                </div>

                <Divider />

                <div className="flex justify-end">
                  <Button
                    type="submit"
                    color="primary"
                    isLoading={isSubmitting}
                    isDisabled={isSubmitting}
                  >
                    Update Availability
                  </Button>
                </div>
              </form>
            </CardBody>
          </Card>
        </Tab>

        <Tab key="pricing" title="Pricing">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Pricing Structure</h3>
            </CardHeader>
            <CardBody>
              <form onSubmit={handleSubmitPricing(onSubmitPricing)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Input
                    type="number"
                    label="Base Price"
                    placeholder="100"
                    min={0}
                    step={0.01}
                    startContent={
                      <div className="pointer-events-none flex items-center">
                        <span className="text-default-400 text-small">KES</span>
                      </div>
                    }
                    {...registerPricing('base_price', { 
                      required: 'Base price is required',
                      min: { value: 0, message: 'Base price cannot be negative' }
                    })}
                    errorMessage={pricingErrors.base_price?.message}
                    isInvalid={!!pricingErrors.base_price}
                  />

                  <Input
                    type="number"
                    label="Price per KM"
                    placeholder="20"
                    min={0}
                    step={0.01}
                    startContent={
                      <div className="pointer-events-none flex items-center">
                        <span className="text-default-400 text-small">KES</span>
                      </div>
                    }
                    {...registerPricing('price_per_km', { 
                      required: 'Price per km is required',
                      min: { value: 0, message: 'Price per km cannot be negative' }
                    })}
                    errorMessage={pricingErrors.price_per_km?.message}
                    isInvalid={!!pricingErrors.price_per_km}
                  />

                  <Input
                    type="number"
                    label="Minimum Order Value"
                    placeholder="500"
                    min={0}
                    step={0.01}
                    startContent={
                      <div className="pointer-events-none flex items-center">
                        <span className="text-default-400 text-small">KES</span>
                      </div>
                    }
                    {...registerPricing('minimum_order_value', { 
                      required: 'Minimum order value is required',
                      min: { value: 0, message: 'Minimum order value cannot be negative' }
                    })}
                    errorMessage={pricingErrors.minimum_order_value?.message}
                    isInvalid={!!pricingErrors.minimum_order_value}
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      {...registerPricing('surge_enabled')}
                      defaultSelected={deliverySettings?.pricing_structure?.surge_pricing?.enabled || false}
                      color="warning"
                    >
                      Enable surge pricing
                    </Switch>
                  </div>

                  <Input
                    type="number"
                    label="Surge Multiplier"
                    placeholder="1.5"
                    min={1}
                    max={5}
                    step={0.1}
                    {...registerPricing('surge_multiplier', { 
                      min: { value: 1, message: 'Multiplier must be at least 1' },
                      max: { value: 5, message: 'Multiplier must be 5 or less' }
                    })}
                    errorMessage={pricingErrors.surge_multiplier?.message}
                    isInvalid={!!pricingErrors.surge_multiplier}
                    className="max-w-xs"
                  />
                </div>

                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h4 className="font-medium text-yellow-900 mb-2">Coming Soon</h4>
                  <p className="text-sm text-yellow-800">
                    Advanced pricing features like custom discounts and dynamic surge conditions will be available soon.
                  </p>
                </div>

                <Divider />

                <div className="flex justify-end">
                  <Button
                    type="submit"
                    color="primary"
                    isLoading={isSubmitting}
                    isDisabled={isSubmitting}
                  >
                    Update Pricing
                  </Button>
                </div>
              </form>
            </CardBody>
          </Card>
        </Tab>

        <Tab key="verification" title="Verification">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Verification Status</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Current Status</span>
                  <Chip
                    color={
                      deliverySettings?.verification_status === "verified" 
                        ? "success" 
                        : deliverySettings?.verification_status === "rejected"
                        ? "danger"
                        : "warning"
                    }
                    variant="flat"
                  >
                    {deliverySettings?.verification_status || "pending"}
                  </Chip>
                </div>

                <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Verification Process</h4>
                  <p className="text-sm text-gray-700">
                    Verification is managed by administrators. Contact support if you need assistance with your verification status.
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>
        </Tab>
      </Tabs>
    </div>
  );
}
