"use server";

import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { auth } from "@/auth";

export interface PackagingOption {
  id: string;
  name: string;
  description?: string;
  price: number;
  vendorId: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePackagingOptionRequest {
  name: string;
  description?: string;
  price: number;
  active?: boolean;
}

export interface UpdatePackagingOptionRequest {
  name?: string;
  description?: string;
  price?: number;
  active?: boolean;
}

// Get current vendor ID from session
const getCurrentVendorId = async (): Promise<string> => {
  const session = await auth();
  if (!session?.vendor?.id) {
    throw new Error("Unauthorized: No vendor found in session");
  }
  return session.vendor.id;
};

// Create a new packaging option
export const createPackagingOption = async (data: CreatePackagingOptionRequest) => {
  try {
    const vendorId = await getCurrentVendorId();
    
    const packagingOption = await api.post<PackagingOption>(
      `vendors/${vendorId}/packaging-options`,
      {
        ...data,
        active: data.active ?? true,
      }
    );

    revalidatePath("/products/packaging-options");
    revalidatePath(`/vendors/${vendorId}/packaging-options`);
    
    return packagingOption;
  } catch (error) {
    console.error("Error creating packaging option:", error);
    throw error;
  }
};

// Update an existing packaging option
export const updatePackagingOption = async (
  id: string, 
  data: UpdatePackagingOptionRequest
) => {
  try {
    const vendorId = await getCurrentVendorId();
    
    const packagingOption = await api.put<PackagingOption>(
      `vendors/${vendorId}/packaging-options/${id}`,
      data
    );

    revalidatePath("/products/packaging-options");
    revalidatePath(`/vendors/${vendorId}/packaging-options`);
    
    return packagingOption;
  } catch (error) {
    console.error("Error updating packaging option:", error);
    throw error;
  }
};

// Delete a packaging option
export const deletePackagingOption = async (id: string) => {
  try {
    const vendorId = await getCurrentVendorId();
    
    await api.destroy(id, `vendors/${vendorId}/packaging-options`);

    revalidatePath("/products/packaging-options");
    revalidatePath(`/vendors/${vendorId}/packaging-options`);
  } catch (error) {
    console.error("Error deleting packaging option:", error);
    throw error;
  }
};

// Get packaging options for current vendor
export const getPackagingOptions = async (searchParams?: Record<string, string>) => {
  try {
    const vendorId = await getCurrentVendorId();
    
    const response = await api.get<PaginatedData<PackagingOption>>(
      `vendors/${vendorId}/packaging-options`,
      searchParams
    );
    
    return response;
  } catch (error) {
    console.error("Error fetching packaging options:", error);
    throw error;
  }
};

// Get a specific packaging option
export const getPackagingOption = async (id: string) => {
  try {
    const vendorId = await getCurrentVendorId();
    
    const packagingOption = await api.get<PackagingOption>(
      `vendors/${vendorId}/packaging-options/${id}`
    );
    
    return packagingOption;
  } catch (error) {
    console.error("Error fetching packaging option:", error);
    throw error;
  }
};

// Toggle active status of a packaging option
export const togglePackagingOptionStatus = async (id: string, active: boolean) => {
  try {
    const vendorId = await getCurrentVendorId();
    
    const packagingOption = await api.put<PackagingOption>(
      `vendors/${vendorId}/packaging-options/${id}`,
      { active }
    );

    revalidatePath("/products/packaging-options");
    revalidatePath(`/vendors/${vendorId}/packaging-options`);
    
    return packagingOption;
  } catch (error) {
    console.error("Error toggling packaging option status:", error);
    throw error;
  }
};

// Server action for form submissions (FormData)
export const createPackagingOptionAction = async (formData: FormData) => {
  const data: CreatePackagingOptionRequest = {
    name: formData.get("name") as string,
    description: formData.get("description") as string || undefined,
    price: parseFloat(formData.get("price") as string),
    active: formData.get("active") === "true",
  };

  return await createPackagingOption(data);
};

// Server action for form submissions (FormData)
export const updatePackagingOptionAction = async (formData: FormData) => {
  const id = formData.get("id") as string;
  const data: UpdatePackagingOptionRequest = {
    name: formData.get("name") as string || undefined,
    description: formData.get("description") as string || undefined,
    price: formData.get("price") ? parseFloat(formData.get("price") as string) : undefined,
    active: formData.get("active") ? formData.get("active") === "true" : undefined,
  };

  return await updatePackagingOption(id, data);
};

// Server action for deletion (FormData)
export const deletePackagingOptionAction = async (formData: FormData) => {
  const id = formData.get("id") as string;
  return await deletePackagingOption(id);
};
