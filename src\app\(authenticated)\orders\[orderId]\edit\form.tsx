"use client";

import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import Select from "react-select";
import { useRouter } from "next/navigation";
import { Icon } from "@/components/icon";
import { Order } from "@/types/order";

export default function OrderForm({
  updateOrder,
  defaultValues,
}: {
  updateOrder: (data: Order) => Promise<void>;
  defaultValues: Order;
}) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm({ defaultValues });

  const order = watch();
  const router = useRouter();

  const onSubmit = async (data: Order) => {
    toast.promise(updateOrder(data), {
      loading: "Updating order...",
      success: "Order updated successfully! 👌",
      error: "Error updating order 🤯",
    }).then(() => {
      router.push("/orders");
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Basic Order Information */}
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <h2 className="text-lg font-semibold mb-4">Order Information</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Staff ID</label>
            <input
              type="text"
              {...register("staffId")}
              className="w-full rounded-lg border-2 border-primary-500 px-4 py-2"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <Select
                  options={[
                    { value: "Pending", label: "Pending" },
                    { value: "Processing", label: "Processing" },
                    { value: "Delivering", label: "Delivering" },
                    { value: "Delivered", label: "Delivered" },
                    { value: "Completed", label: "Completed" },
                    { value: "Cancelled", label: "Cancelled" },
                  ]}
                  onChange={(v) => setValue("status", v?.value!)}
                  defaultValue={{
                    value: defaultValues.status,
                    label: defaultValues.status,
                  }}
                  isSearchable
                  placeholder="Select a status"
                  classNames={{
                    control: () =>
                      "!border !border-primary-500 py-[6px] px-2 !rounded-lg w-full",
                  }}
                />
              )}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Reference</label>
            <input
              type="text"
              {...register("ref")}
              className="w-full rounded-lg border-2 border-primary-500 px-4 py-2"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Accepted At</label>
            <input
              type="datetime-local"
              {...register("acceptedAt")}
              className="w-full rounded-lg border-2 border-primary-500 px-4 py-2"
            />
          </div>
        </div>
      </div>

      {/* Order Items - Coming Soon */}
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Order Items</h2>
        </div>
        <div className="text-center py-8">
          <Icon 
            name="icon-[heroicons--wrench-screwdriver-20-solid]" 
            classNames="w-12 h-12 text-gray-400 mx-auto mb-4" 
          />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Coming Soon</h3>
          <p className="text-gray-500">Order items management will be available in the next update.</p>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={isSubmitting}
          className="inline-flex items-center gap-2 px-6 py-3 text-sm font-medium text-white bg-primary hover:bg-primary-dark rounded-lg"
        >
          {isSubmitting ? "Updating..." : "Update Order"}
        </button>
      </div>
    </form>
  );
}
