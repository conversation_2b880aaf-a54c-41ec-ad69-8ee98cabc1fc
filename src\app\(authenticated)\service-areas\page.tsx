import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect } from "next/navigation";
import ServiceAreasTable from "@/components/tables/service-areas-table";
import CoverageChecker from "@/components/delivery/coverage-checker";
import { revalidatePath } from "next/cache";
import { PaginatedData, ServiceArea } from "@/types";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

const fetchServiceAreas = cache(
  (vendorId: string, searchParams?: Record<string, string>) =>
    api.get<PaginatedData<ServiceArea>>(
      `vendors/${vendorId}/service-areas`,
      searchParams,
    ),
);

export const generateMetadata = async () => {
  return {
    title: "Service Areas",
    description: "Manage your delivery service areas and coverage zones",
    keywords: ["service areas", "delivery", "coverage"],
  };
};

export default async function ServiceAreasPage({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();

  // Check if user is authenticated and has vendor access
  if (!session) {
    redirect('/login');
  }

  // Get vendor ID from session
  const vendorId = session?.vendor?.id || session?.branch?.vendorId;
  
  if (!vendorId) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            No Vendor Access
          </h2>
          <p className="text-gray-600">
            You need to be associated with a vendor to manage service areas.
          </p>
        </div>
      </div>
    );
  }

  const serviceAreas = await fetchServiceAreas(vendorId, searchParams);

  const deleteServiceArea = async (data: FormData) => {
    "use server";

    const serviceAreaId = data.get("id") as string;
    await api.destroy(serviceAreaId, `vendors/${vendorId}/service-areas`);
    revalidatePath("/service-areas");
  };

  const toggleActiveStatus = async (serviceAreaId: string, active: boolean) => {
    "use server";

    await api.patch(`vendors/${vendorId}/service-areas/${serviceAreaId}`, {
      active,
    });
    revalidatePath("/service-areas");
  };

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Service Areas</h1>
        <p className="text-gray-600 mt-1">
          Manage your delivery coverage areas and zones
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div className="lg:col-span-2">
          {serviceAreas && (
            <ServiceAreasTable
              data={serviceAreas.data}
              meta={serviceAreas.meta}
              deleteAction={deleteServiceArea}
              toggleActiveAction={toggleActiveStatus}
              vendorId={vendorId}
            />
          )}
        </div>

        <div className="lg:col-span-1">
          <CoverageChecker vendorId={vendorId} />
        </div>
      </div>
    </div>
  );
}
