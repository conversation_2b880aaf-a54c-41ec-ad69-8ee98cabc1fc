"use client";

import React, { useState } from "react";
import { CustomTable } from "../../custom-table";
import Link from "next/link";
import { 
  Chip, 
  Button, 
  Dropdown, 
  DropdownTrigger, 
  Dropdown<PERSON>enu, 
  Dropdown<PERSON><PERSON>,
  <PERSON>dal,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  Modal<PERSON>ooter,
  useDisclosure,
} from "@nextui-org/react";
import { Icon } from "../../icon";
import { MoreVertical, Eye, CheckCircle, XCircle, Pause, Star } from "lucide-react";
import toast from "react-hot-toast";
// Using global PaginationMeta type from next-models.d.ts

interface DeliveryVendor {
  id: string;
  name: string;
  email: string;
  phone: string;
  verification_status: 'pending' | 'verified' | 'rejected' | 'suspended';
  service_areas_count: number;
  coverage_regions: string[];
  performance_metrics: {
    delivery_success_rate: number;
    average_delivery_time: number;
    total_deliveries: number;
    customer_rating: number;
  };
  last_activity: string;
  created_at: string;
  updated_at: string;
}

interface AdminDeliveryVendorsTableProps {
  data: DeliveryVendor[];
  meta: PaginationMeta;
  updateVendorStatus: (data: FormData) => Promise<void>;
  suspendVendor: (data: FormData) => Promise<void>;
  bulkUpdateVendors: (data: FormData) => Promise<void>;
}

export default function AdminDeliveryVendorsTable({
  data,
  meta,
  updateVendorStatus,
  suspendVendor,
  bulkUpdateVendors,
}: AdminDeliveryVendorsTableProps) {
  const [selectedVendor, setSelectedVendor] = useState<DeliveryVendor | null>(null);
  const [actionType, setActionType] = useState<'verify' | 'reject' | 'suspend' | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleStatusUpdate = async (vendorId: string, status: string) => {
    const formData = new FormData();
    formData.append("vendorId", vendorId);
    formData.append("status", status);

    toast.promise(updateVendorStatus(formData), {
      loading: `Updating vendor status...`,
      success: `Vendor status updated successfully!`,
      error: "Failed to update vendor status",
    });
  };

  const handleSuspend = async (vendorId: string) => {
    const formData = new FormData();
    formData.append("vendorId", vendorId);

    toast.promise(suspendVendor(formData), {
      loading: "Suspending vendor...",
      success: "Vendor suspended successfully!",
      error: "Failed to suspend vendor",
    });
  };

  const confirmAction = () => {
    if (!selectedVendor || !actionType) return;

    switch (actionType) {
      case 'verify':
        handleStatusUpdate(selectedVendor.id, 'verified');
        break;
      case 'reject':
        handleStatusUpdate(selectedVendor.id, 'rejected');
        break;
      case 'suspend':
        handleSuspend(selectedVendor.id);
        break;
    }

    onClose();
    setSelectedVendor(null);
    setActionType(null);
  };

  const openConfirmModal = (vendor: DeliveryVendor, action: 'verify' | 'reject' | 'suspend') => {
    setSelectedVendor(vendor);
    setActionType(action);
    onOpen();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'danger';
      case 'suspended':
        return 'default';
      default:
        return 'default';
    }
  };

  const getPerformanceColor = (rate: number) => {
    if (rate >= 95) return 'success';
    if (rate >= 85) return 'warning';
    return 'danger';
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <>
      <CustomTable
        title="Delivery Vendors"
        columns={[
          {
            name: "Vendor",
            uid: "vendor",
            sortable: true,
            renderCell: (vendor: DeliveryVendor) => (
              <div className="flex flex-col">
                <p className="font-semibold">{vendor.name}</p>
                <p className="text-sm text-default-500">{vendor.email}</p>
                <p className="text-xs text-default-400">{vendor.phone}</p>
              </div>
            ),
          },
          {
            name: "Status",
            uid: "verification_status",
            sortable: true,
            renderCell: (vendor: DeliveryVendor) => (
              <Chip
                size="sm"
                variant="flat"
                color={getStatusColor(vendor.verification_status) as any}
                className="capitalize"
              >
                {vendor.verification_status}
              </Chip>
            ),
          },
          {
            name: "Service Areas",
            uid: "service_areas",
            renderCell: (vendor: DeliveryVendor) => (
              <div className="flex flex-col">
                <span className="font-medium">{vendor.service_areas_count} areas</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {vendor.coverage_regions.slice(0, 2).map((region, index) => (
                    <Chip key={index} size="sm" variant="bordered" className="text-xs">
                      {region}
                    </Chip>
                  ))}
                  {vendor.coverage_regions.length > 2 && (
                    <Chip size="sm" variant="bordered" className="text-xs">
                      +{vendor.coverage_regions.length - 2}
                    </Chip>
                  )}
                </div>
              </div>
            ),
          },
          {
            name: "Performance",
            uid: "performance",
            renderCell: (vendor: DeliveryVendor) => (
              <div className="flex flex-col space-y-1">
                <div className="flex items-center space-x-2">
                  <Chip
                    size="sm"
                    variant="flat"
                    color={getPerformanceColor(vendor.performance_metrics.delivery_success_rate) as any}
                  >
                    {vendor.performance_metrics.delivery_success_rate}% success
                  </Chip>
                </div>
                <div className="text-xs text-default-500">
                  {vendor.performance_metrics.average_delivery_time}min avg
                </div>
                <div className="flex items-center space-x-1">
                  <Star className="size-3 text-yellow-500 fill-current" />
                  <span className="text-xs">{vendor.performance_metrics.customer_rating}</span>
                </div>
              </div>
            ),
          },
          {
            name: "Deliveries",
            uid: "deliveries",
            sortable: true,
            renderCell: (vendor: DeliveryVendor) => (
              <span className="font-medium">
                {vendor.performance_metrics.total_deliveries.toLocaleString()}
              </span>
            ),
          },
          {
            name: "Last Activity",
            uid: "last_activity",
            sortable: true,
            renderCell: (vendor: DeliveryVendor) => (
              <span className="text-sm text-default-500">
                {formatTime(vendor.last_activity)}
              </span>
            ),
          },
          {
            name: "Actions",
            uid: "actions",
            renderCell: (vendor: DeliveryVendor) => (
              <div className="flex items-center gap-2">
                <Button
                  as={Link}
                  href={`/admin/delivery-management/vendors/${vendor.id}`}
                  isIconOnly
                  size="sm"
                  variant="light"
                >
                  <Eye className="size-4" />
                </Button>
                
                <Dropdown>
                  <DropdownTrigger>
                    <Button isIconOnly size="sm" variant="light">
                      <MoreVertical className="size-4" />
                    </Button>
                  </DropdownTrigger>
                  <DropdownMenu>
                    {vendor.verification_status === 'pending' ? (
                      <>
                        <DropdownItem
                          key="verify"
                          startContent={<CheckCircle className="size-4" />}
                          onPress={() => openConfirmModal(vendor, 'verify')}
                        >
                          Verify Vendor
                        </DropdownItem>
                        <DropdownItem
                          key="reject"
                          startContent={<XCircle className="size-4" />}
                          className="text-danger"
                          color="danger"
                          onPress={() => openConfirmModal(vendor, 'reject')}
                        >
                          Reject Vendor
                        </DropdownItem>
                      </>
                    ) : vendor.verification_status === 'verified' ? (
                      <DropdownItem
                        key="suspend"
                        startContent={<Pause className="size-4" />}
                        className="text-warning"
                        color="warning"
                        onPress={() => openConfirmModal(vendor, 'suspend')}
                      >
                        Suspend Vendor
                      </DropdownItem>
                    ) : vendor.verification_status === 'rejected' ? (
                      <DropdownItem
                        key="verify"
                        startContent={<CheckCircle className="size-4" />}
                        onPress={() => openConfirmModal(vendor, 'verify')}
                      >
                        Verify Vendor
                      </DropdownItem>
                    ) : null}
                  </DropdownMenu>
                </Dropdown>
              </div>
            ),
          },
        ]}
        data={data}
        meta={meta}
        filter={{
          column: "verification_status",
          displayName: "Status",
          values: [
            { name: "Verified", value: "verified" },
            { name: "Pending", value: "pending" },
            { name: "Rejected", value: "rejected" },
            { name: "Suspended", value: "suspended" },
          ],
        }}
      />

      {/* Confirmation Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          <ModalHeader>
            Confirm Action
          </ModalHeader>
          <ModalBody>
            {selectedVendor && actionType && (
              <p>
                Are you sure you want to{" "}
                <strong className="capitalize">{actionType}</strong>{" "}
                <strong>{selectedVendor.name}</strong>?
                {actionType === 'suspend' && (
                  <span className="block mt-2 text-sm text-warning">
                    This will prevent the vendor from accepting new orders.
                  </span>
                )}
                {actionType === 'reject' && (
                  <span className="block mt-2 text-sm text-danger">
                    This will deny the vendor&apos;s verification request.
                  </span>
                )}
              </p>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              Cancel
            </Button>
            <Button
              color={actionType === 'verify' ? 'success' : actionType === 'reject' ? 'danger' : 'warning'}
              onPress={confirmAction}
            >
              Confirm {actionType}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}
