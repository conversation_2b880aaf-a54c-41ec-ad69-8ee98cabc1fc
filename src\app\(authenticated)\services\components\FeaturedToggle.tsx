"use client";

import { useState } from "react";
import { toast } from "react-hot-toast";

interface FeaturedToggleProps {
  service: Service;
  onToggle: (service: Service, featured: boolean) => Promise<void>;
}

export default function FeaturedToggle({ service, onToggle }: FeaturedToggleProps) {
  const [isFeatured, setIsFeatured] = useState(service.featured || false);

  const handleToggle = async () => {
    const newFeaturedStatus = !isFeatured;
    setIsFeatured(newFeaturedStatus);

    toast.promise(
      onToggle(service, newFeaturedStatus),
      {
        loading: "Updating featured status...",
        success: `Service ${newFeaturedStatus ? "featured" : "unfeatured"}!`,
        error: "Failed to update featured status",
      }
    );
  };

  return (
    <div className="flex items-center gap-2">
      <button
        onClick={handleToggle}
        className={`flex items-center justify-center w-6 h-6 rounded-full transition-colors ${
          isFeatured 
            ? "text-yellow-500 hover:text-yellow-600" 
            : "text-gray-400 hover:text-gray-600"
        }`}
        title={isFeatured ? "Remove from featured" : "Mark as featured"}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill={isFeatured ? "currentColor" : "none"}
          stroke="currentColor"
          className="w-5 h-5"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z"
          />
        </svg>
      </button>
      {isFeatured && (
        <span className="text-xs text-yellow-600 font-medium">Featured</span>
      )}
    </div>
  );
} 