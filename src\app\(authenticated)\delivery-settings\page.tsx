import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import DeliverySettingsForm from "@/components/forms/delivery-settings-form";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface DeliverySettings {
  delivery_preferences?: {
    max_distance?: number;
    preferred_vehicle_types?: string[];
    working_hours?: {
      start: string;
      end: string;
      days: number[];
    };
    auto_accept_orders?: boolean;
    max_orders_per_day?: number;
  };
  availability_settings?: {
    is_available?: boolean;
    current_status?: string;
    break_times?: Array<{
      start: string;
      end: string;
    }>;
    unavailable_dates?: string[];
  };
  pricing_structure?: {
    base_price?: number;
    price_per_km?: number;
    minimum_order_value?: number;
    surge_pricing?: {
      enabled: boolean;
      multiplier: number;
      conditions: string[];
    };
    discounts?: Array<{
      type: string;
      value: number;
      conditions: string[];
    }>;
  };
  verification_status?: string;
}

const fetchDeliverySettings = cache(
  (vendorId: string) =>
    api.get<DeliverySettings>(`vendors/${vendorId}/delivery-settings`),
);

export const generateMetadata = async () => {
  return {
    title: "Delivery Settings",
    description: "Manage your delivery preferences, availability, and pricing",
    keywords: ["delivery", "settings", "preferences", "pricing"],
  };
};

export default async function DeliverySettingsPage() {
  const session = await auth();

  // Check if user is authenticated and has vendor access
  if (!session) {
    redirect('/login');
  }

  // Get vendor ID from session
  const vendorId = session?.vendor?.id || session?.branch?.vendorId;
  
  if (!vendorId) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            No Vendor Access
          </h2>
          <p className="text-gray-600">
            You need to be associated with a vendor to manage delivery settings.
          </p>
        </div>
      </div>
    );
  }

  // Fetch current delivery settings
  const deliverySettings = await fetchDeliverySettings(vendorId);

  const updateDeliveryPreferences = async (data: FormData) => {
    "use server";

    try {
      await api.put(`vendors/${vendorId}/delivery-settings/preferences`, data);
      revalidatePath("/delivery-settings");
    } catch (error) {
      console.error("Failed to update delivery preferences:", error);
      throw error;
    }
  };

  const updateAvailabilitySettings = async (data: FormData) => {
    "use server";

    try {
      await api.put(`vendors/${vendorId}/delivery-settings/availability`, data);
      revalidatePath("/delivery-settings");
    } catch (error) {
      console.error("Failed to update availability settings:", error);
      throw error;
    }
  };

  const updatePricingStructure = async (data: FormData) => {
    "use server";

    try {
      await api.put(`vendors/${vendorId}/delivery-settings/pricing`, data);
      revalidatePath("/delivery-settings");
    } catch (error) {
      console.error("Failed to update pricing structure:", error);
      throw error;
    }
  };

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Delivery Settings</h1>
        <p className="text-gray-600 mt-1">
          Configure your delivery preferences, availability, and pricing
        </p>
      </div>

      <DeliverySettingsForm
        deliverySettings={deliverySettings}
        vendorId={vendorId}
        updatePreferences={updateDeliveryPreferences}
        updateAvailability={updateAvailabilitySettings}
        updatePricing={updatePricingStructure}
      />
    </div>
  );
}
