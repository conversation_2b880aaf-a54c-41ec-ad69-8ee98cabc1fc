// pages/api/addRole.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { api } from "@/lib/api"; // Adjust path as necessary

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    const { user_id, role } = req.body;

    try {
      // Make the call to the backend API
      const response = await api.post<{ data: any }>("auth/addroletouser", { user_id, role });

      // Ensure response is defined before accessing its data
      if (response?.data) {
        res.status(200).json(response.data);
      } else {
        res.status(500).json({ message: 'Unexpected response structure from backend API' });
      }
    } catch (error) {
      console.error("Error assigning role:", error);
      res.status(500).json({ message: 'Error assigning role', error });
    }
  } else {
    // Handle invalid HTTP methods
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
