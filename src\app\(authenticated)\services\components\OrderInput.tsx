"use client";

import { useState, useRef, useEffect } from "react";
import { toast } from "react-hot-toast";

interface OrderInputProps {
  service: Service;
  onUpdate: (service: Service, order: number) => Promise<void>;
}

export default function OrderInput({ service, onUpdate }: OrderInputProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [orderValue, setOrderValue] = useState(service.order || 0);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    const newOrder = parseInt(orderValue.toString());
    
    if (isNaN(newOrder)) {
      setOrderValue(service.order || 0);
      setIsEditing(false);
      return;
    }

    if (newOrder === service.order) {
      setIsEditing(false);
      return;
    }

    toast.promise(
      onUpdate(service, newOrder),
      {
        loading: "Updating order...",
        success: "Order updated!",
        error: "Failed to update order",
      }
    );

    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSave();
    } else if (e.key === "Escape") {
      setOrderValue(service.order || 0);
      setIsEditing(false);
    }
  };

  const handleBlur = () => {
    handleSave();
  };

  if (isEditing) {
    return (
      <input
        ref={inputRef}
        type="number"
        value={orderValue}
        onChange={(e) => setOrderValue(parseInt(e.target.value) || 0)}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        min="0"
      />
    );
  }

  return (
    <button
      onClick={handleEdit}
      className="px-2 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors cursor-pointer"
      title="Click to edit order"
    >
      {service.order || 0}
    </button>
  );
} 