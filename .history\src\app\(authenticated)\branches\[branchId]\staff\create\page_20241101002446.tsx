import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import BranchStaffCreateForm from "./form";
import { Metadata } from "next";
import { cache } from "react";
import Link from "next/link";
import { toast } from "react-hot-toast";

// Define the response structure
interface StaffResponse {
  userId?: string;
  [key: string]: any; // Add any other properties that might come with the response
}

const fetchBranch = cache((branchId: string) =>
  api.get<Branch>(`/branches/${branchId}`)
);

export const metadata: Metadata = {
  title: "Create Staff",
  description: "Create Staff",
  keywords: ["staff"],
};

export default async function Page({
  params,
}: {
  params: {
    branchId: string;
  };
}) {
  const branch = await fetchBranch(params.branchId);

  // const createStaff = async (formData: FormData) => {
  //   "use server";

  //   try {
  //     if (branch) {
  //       formData.append("branchId", params.branchId);
  //       formData.append("vendorId", branch.vendorId);
  //     }

  //     // Step 1: Create the staff member
  //     const staffResponse = await api.post<StaffResponse>(`branches/${params.branchId}/staff`, formData);
      
  //     // Step 2: Assign role to the created staff if userId is present
  //     if (staffResponse?.userId) {
  //       const roleData = {
  //         user_id: staffResponse.userId,
  //         role: formData.get("role"),
  //       };
        
  //       await api.post("auth/addroletouser", roleData);
  //     }

  //     revalidatePath(`/branches/${params.branchId}/staff`);
  //     toast.success("Staff created and role assigned successfully!");
  //   } catch (error) {
  //     console.error("Error creating staff or assigning role:", error);
  //     toast.error("Failed to create staff or assign role.");
  //   }
  // };


  const createStaff = async (formData: FormData) => {
    "use server";
  
    try {
      if (branch) {
        formData.append("branchId", params.branchId);
        formData.append("vendorId", branch.vendorId);
      }
  
      // Log form data for debugging
      console.log("Form Data being sent:");
      for (const [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
      }
  
      // Step 1: Create the staff member
      const staffResponse = await api.post<StaffResponse>(`branches/${params.branchId}/staff`, formData);
      console.log("Staff creation response:", staffResponse);
  
      // Step 2: Assign role to the created staff
      if (staffResponse && staffResponse.userId) {
        const roleData = {
          user_id: staffResponse.userId,
          role: formData.get("role") as string, // Use 'as string' to assert the type
        };
        
        const roleAssignResponse = await api.post("auth/addroletouser", roleData);
        console.log("Role assignment response:", roleAssignResponse);
      }
  
      revalidatePath(`/branches/${params.branchId}/staff`);
      toast.success("Staff created and role assigned successfully!");
    } catch (error) {
      console.error("Error during staff creation or role assignment:", error);
      toast.error("Failed to create staff or assign role.");
    }
  };
  const createStaff = async (formData: FormData) => {
    "use server";
  
    try {
      if (branch) {
        formData.append("branchId", params.branchId);
        formData.append("vendorId", branch.vendorId);
      }
  
      // Log form data for debugging
      console.log("Form Data being sent:");
      for (const [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
      }
  
      // Step 1: Create the staff member
      const staffResponse = await api.post<StaffResponse>(`branches/${params.branchId}/staff`, formData);
      console.log("Staff creation response:", staffResponse);
  
      // Step 2: Assign role to the created staff
      if (staffResponse && staffResponse.userId) {
        const roleData = {
          user_id: staffResponse.userId,
          role: formData.get("role") as string, // Use 'as string' to assert the type
        };
        
        const roleAssignResponse = await api.post("auth/addroletouser", roleData);
        console.log("Role assignment response:", roleAssignResponse);
      }
  
      revalidatePath(`/branches/${params.branchId}/staff`);
      toast.success("Staff created and role assigned successfully!");
    } catch (error) {
      console.error("Error during staff creation or role assignment:", error);
      toast.error("Failed to create staff or assign role.");
    }
  };
    
  return (
    <div className="p-4">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="text-2xl text-primary">
          {branch ? `Create Staff for ${branch.name}` : "Create Staff"}
        </h1>

        <Link
          href={`/branches/${params.branchId}/staff`}
          className="flex items-center space-x-2 text-default-500"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="h-6 w-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6.75 15.75 3 12m0 0 3.75-3.75M3 12h18"
            />
          </svg>
          <span>Back to Staff</span>
        </Link>
      </div>
      {branch && (
        <BranchStaffCreateForm storeStaff={createStaff} branch={branch} />
      )}
    </div>
  );
}
