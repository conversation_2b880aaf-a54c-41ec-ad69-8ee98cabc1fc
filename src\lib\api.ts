import { notFound, redirect } from "next/navigation";
import axios, {
  AxiosError,
  AxiosResponse,
  AxiosInstance,
  AxiosRequestConfig,
  InternalAxiosRequestConfig,
} from "axios";
import { auth } from "@/auth";

const API_BASE_URL = process.env.API_BASE_URL;
const API_BASE_VERSION = process.env.API_BASE_VERSION || "v1";

interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
}

const http: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/${API_BASE_VERSION}`,
  withCredentials: true,
  headers: {
    Accept: "Application/json",
    "Content-Type": "Application/json",
  },
});

const handleResponse = (response: AxiosResponse | undefined) => response;

const handleError = (reason: string | number = "expired") => {
  console.error(reason);
};

http.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    const session: any = await auth();

    if (config.headers && session) {
      config.headers.Authorization = `Bearer ${session.accessToken}`;
    } else {
      console.warn("Auth session not found or headers missing for request");
    }

    return config;
  },
  (error) => Promise.reject(error),
);

http.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as CustomAxiosRequestConfig;
    
    if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const session: any = await auth();
        if (session?.refreshToken) {
          const response = await http.post("auth/refresh", {
            refreshToken: session.refreshToken,
          });

          if (response.data) {
            // Update the session with new tokens
            session.accessToken = response.data.token;
            session.refreshToken = response.data.refreshToken;
            session.expiresAt = response.data.expiresAt;

            // Retry the original request
            originalRequest.headers.Authorization = `Bearer ${response.data.token}`;
            return http(originalRequest);
          }
        }
      } catch (refreshError) {
        console.error("Failed to refresh token:", refreshError);
        // Use Next.js redirect instead of window.location
        redirect('/login');
      }
    }

    if (error.response) {
      console.error(`API Error: ${error.response.status}`, error.response.data);
    } else if (error.request) {
      console.error("API No Response Error:", error.request);
    } else {
      console.error("API Request Setup Error:", error.message);
    }
    
    return Promise.reject(error);
  },
);

const buildQueryString = (query?: Record<string, string | number | string[] | undefined>): string => {
  if (query) {
    const keys = Object.keys(query);
    return keys.length
      ? "?" + keys
          .filter(k => query[k] !== undefined)
          .map((k) => {
            const value = query[k];
            if (Array.isArray(value)) {
              return value.map(v => `${k}=${v}`).join('&');
            }
            return `${k}=${value}`;
          })
          .join("&")
      : "";
  }

  return "";
};

export const imagePath = (
  path: string | undefined | null,
  defaultImage: string | undefined | null = null,
): string => {
  if (path?.includes("amazonaws")) {
    return path;
  }

  return path
    ? `${API_BASE_URL || "http://192.168.100.32:3333"}${path}`
    : defaultImage || "/images/icon.png";
};

export interface BulkUploadResponse {
  jobId: string;
}

export interface JobStatusResponse {
  id: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress?: number; // 0-100
  totalRows?: number;
  processedRows?: number;
  failedRows?: number;
  errors?: Array<{ row: number; message: string }>;
  results?: any; // Depending on what the job returns upon completion
  createdAt: string;
  updatedAt: string;
}

const transformPaginationMeta = (meta: any) => {
  if (!meta) return meta;
  return {
    total: meta.total,
    perPage: meta.per_page,
    currentPage: meta.current_page,
    lastPage: meta.last_page,
    firstPage: meta.first_page,
    firstPageUrl: meta.first_page_url,
    lastPageUrl: meta.last_page_url,
    nextPageUrl: meta.next_page_url,
    previousPageUrl: meta.previous_page_url,
  };
};

export const api: {
  [x: string]: any;
  get: <T>(
    path: string,
    query?: Record<string, string | number | string[] | undefined>,
    options?: AxiosRequestConfig,
  ) => Promise<T | undefined>;
  post: <T>(
    path: string,
    payload?: Record<string, unknown> | Partial<T> | FormData,
    options?: AxiosRequestConfig,
  ) => Promise<T | undefined>;
  put: <T>(
    path: string,
    payload?: Record<string, unknown> | Partial<T> | FormData,
    options?: AxiosRequestConfig,
  ) => Promise<T | undefined>;
  patch: <T>(
    path: string,
    payload?: Record<string, unknown> | Partial<T> | FormData,
    options?: AxiosRequestConfig,
  ) => Promise<T | undefined>;
  destroy: <T>(
    id: number | string,
    path?: string,
    options?: AxiosRequestConfig,
  ) => Promise<T | undefined>;
  uploadBulkProducts: (
    vendorId: string,
    payload: FormData,
    options?: AxiosRequestConfig,
  ) => Promise<BulkUploadResponse | undefined>;
  getJobStatus: (
    jobId: string,
    options?: AxiosRequestConfig,
  ) => Promise<JobStatusResponse | undefined>;
} = {
  get: async <T>(
    path: string,
    query?: Record<string, string | number | string[] | undefined>,
    options?: AxiosRequestConfig,
  ): Promise<T | undefined> => {
    const { data } = await http.get(path + buildQueryString(query), options);
    if (data && 'meta' in data) {
      return {
        ...data,
        meta: transformPaginationMeta(data.meta),
      };
    }
    return data;
  },

  post: async <T>(
    path: string,
    payload = {},
    options?: AxiosRequestConfig,
  ): Promise<T | undefined> => {
    const { data } = await http.post(path, payload, options);
    return data;
  },

  put: async <T>(
    path: string,
    payload = {},
    options?: AxiosRequestConfig,
  ): Promise<T | undefined> => {
    const { data } = await http.put(path, payload, options);
    return data;
  },
  patch: async <T>(
    path: string,
    payload = {},
    options?: AxiosRequestConfig,
  ): Promise<T | undefined> => {
    const { data } = await http.patch(path, payload, options);
    return data;
  },
  destroy: async <T>(
    id: number | string,
    path?: string,
    options?: AxiosRequestConfig,
  ): Promise<T | undefined> => {
    const { data } = await http.delete(`/${path}/${id}`, options);
    return data;
  },

  uploadBulkProducts: async (
    vendorId: string,
    payload: FormData,
    options?: AxiosRequestConfig,
  ): Promise<BulkUploadResponse | undefined> => {
    const { data } = await http.post<BulkUploadResponse>(
      `/vendors/${vendorId}/products/bulk-upload`,
      payload,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        ...options,
      },
    );
    return data;
  },

  getJobStatus: async (
    jobId: string,
    options?: AxiosRequestConfig,
  ): Promise<JobStatusResponse | undefined> => {
    const { data } = await http.get<JobStatusResponse>(`/jobs/${jobId}`, options);
    return data;
  },
};