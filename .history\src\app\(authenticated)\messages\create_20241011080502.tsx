"use client";

import React, { useEffect, useState } from "react";
import { useForm, Controller } from "react-hook-form";
import AsyncSelect from "react-select/async";
import Select from "react-select";
import { toast } from "react-hot-toast";
import { useSession } from "next-auth/react";

export default function BroadcastPage() {
  const { data: session } = useSession();
  const [creating, setCreating] = useState(false); // Controls the side drawer state
  const [groups, setGroups] = useState([]);
  const [messages, setMessages] = useState([]);
  const [templates, setTemplates] = useState([]);

  const {
    handleSubmit,
    watch,
    control,
    setValue,
    reset,
    formState: { isSubmitting },
  } = useForm({
    defaultValues: {
      type: "",
      receipients: [],
      groups: [],
      channels: [],
      templateId: "",
      message: "",
      branchId: session?.branch?.id || "",
      vendorId: session?.vendor?.id || "",
      userId: session?.id || "",
    },
  });

  const broadcast = watch();

  // Load messages, groups, and templates on component mount
  useEffect(() => {
    const loadMessages = async () => {
      const response = await fetch("/api/messages");
      const data = await response.json();
      setMessages(data);
    };

    const loadGroups = async () => {
      const response = await fetch(`/api/branches/${session?.branch?.id}/groups`);
      const data = await response.json();
      setGroups(data.map((g) => ({ value: g.id, label: g.name })));
    };

    const loadTemplates = async () => {
      const response = await fetch("/api/message-templates");
      const data = await response.json();
      setTemplates(data.map((template) => ({ value: template.id, label: template.name, content: template.content })));
    };

    if (session?.branch?.id) {
      loadGroups();
    }
    loadMessages();
    loadTemplates();
  }, [session]);

  const sendMessage = async (data) => {
    try {
      await fetch("/api/messages", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      toast.success("Message sent successfully!");
      setCreating(false);
      reset();
    } catch (error) {
      console.error(error);
      toast.error("Failed to send message");
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-xl font-bold">Broadcast Messages</h1>

      {/* List of previous messages */}
      <div className="my-4">
        {messages.map((msg) => (
          <div key={msg.id} className="p-4 border rounded-lg mb-2">
            <div className="flex justify-between">
              <div>
                <strong>{msg.template?.name}</strong>
                <p>{msg.template?.content}</p>
              </div>
              <div>
                <button
                  onClick={() => {
                    setValue("id", msg.id);
                    setValue("type", "individual");
                    setValue("templateId", msg.template?.id);
                    setValue("message", msg.template?.content);
                    setCreating(true); // Open side drawer
                  }}
                  className="bg-primary text-white px-4 py-2 rounded"
                >
                  Resend
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* New message form */}
      <button onClick={() => setCreating(true)} className="bg-primary text-white px-4 py-2 rounded">
        Create New Message
      </button>

      {creating && (
        <div className="fixed inset-y-0 right-0 bg-white p-6 shadow-lg w-full max-w-md z-50 transition-transform transform translate-x-0">
          <button
            onClick={() => setCreating(false)}
            className="text-red-500 absolute top-2 right-2"
          >
            &times;
          </button>
          <h2 className="text-xl font-bold mb-4">New Broadcast Message</h2>
          <form onSubmit={handleSubmit(sendMessage)} className="space-y-4">
            {/* Select type */}
            <div>
              <label>Select type</label>
              <Controller
                name="type"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={[
                      { value: "all", label: "All customers" },
                      { value: "groups", label: "Customer groups" },
                      { value: "individual", label: "Individual customers" },
                    ]}
                    onChange={(option) => setValue("type", option.value)}
                  />
                )}
              />
            </div>

            {/* Select recipients based on type */}
            {broadcast.type === "individual" && (
              <div>
                <label>Select recipients</label>
                <Controller
                  name="receipients"
                  control={control}
                  render={({ field }) => (
                    <AsyncSelect
                      {...field}
                      isMulti
                      loadOptions={async (inputValue) => {
                        const res = await fetch(`/api/customers?q=${inputValue}`);
                        const customers = await res.json();
                        return customers.map((customer) => ({ value: customer.id, label: customer.name }));
                      }}
                      placeholder="Search for customers"
                    />
                  )}
                />
              </div>
            )}

            {broadcast.type === "groups" && (
              <div>
                <label>Select groups</label>
                <Controller
                  name="groups"
                  control={control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      isMulti
                      options={groups}
                      placeholder="Select groups"
                    />
                  )}
                />
              </div>
            )}

            {/* Select a message template */}
            <div>
              <label>Select a message template</label>
              <Controller
                name="templateId"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={templates}
                    onChange={(option) => {
                      setValue("templateId", option.value);
                      setValue("message", option.content);
                    }}
                  />
                )}
              />
            </div>

            {/* Display the message content */}
            {broadcast.message && (
              <div>
                <label>Message</label>
                <textarea
                  {...field}
                  className="w-full p-2 border rounded"
                  value={broadcast.message}
                  readOnly
                />
              </div>
            )}

            {/* Select channels */}
            <div>
              <label>Select channel(s)</label>
              <div className="grid grid-cols-2 gap-4">
                {["In App", "SMS", "Email"].map((channel) => (
                  <div key={channel} className="flex items-center">
                    <input
                      type="checkbox"
                      value={channel.toLowerCase()}
                      {...register("channels")}
                      className="mr-2"
                    />
                    <label>{channel}</label>
                  </div>
                ))}
              </div>
            </div>

            {/* Submit button */}
            <button type="submit" className="bg-primary text-white px-4 py-2 rounded w-full" disabled={isSubmitting}>
              Send message
            </button>
          </form>
        </div>
      )}
    </div>
  );
}
