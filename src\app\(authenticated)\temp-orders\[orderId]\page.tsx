import { api, imagePath } from "@/lib/api";
import { formatDate } from "date-fns";
import Image from "next/image";
import Link from "next/link";
import { cache } from "react";
import { Chip } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import { TempOrder } from "@/types/temp-order";

// Fetch order details from the API
const fetchOrder = cache((id: string) => api.get<TempOrder>(`temp-orders/${id}`));

export const generateMetadata = async ({ params }: { params: { orderId: string } }) => {
  const order = await fetchOrder(params.orderId);

  return {
    title: order
      ? `Order ${order?.id.toUpperCase()} - ${formatDate(
          new Date(order?.createdAt),
          "MMM dd, yyyy hh:mm a",
        )}`
      : "Order",
  };
};

export default async function OrderDetailsPage({ params }: { params: { orderId: string } }) {
  const order = await fetchOrder(params.orderId);

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-gray-900">Order Not Found</h1>
          <p className="mt-2 text-gray-600">The order you&apos;re looking for doesn&apos;t exist or has been removed.</p>
          <Link
            href="/temp-orders"
            className="mt-4 inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-primary hover:bg-primary/10 rounded-lg"
          >
            <Icon name="icon-[mingcute--arrow-left-line]" />
            Back to Orders
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold">Order Details</h1>
              <p className="text-sm text-gray-500">Order ID: {order.id}</p>
            </div>
            <div className="flex items-center gap-3">
              <Link
                href={`/temp-orders/${order.id}/edit`}
                className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-primary hover:bg-primary/10 rounded-lg"
              >
                <Icon name="icon-[mage--edit-pen-fill]" />
                Edit Order
              </Link>
              <Link
                href="/temp-orders"
                className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-lg"
              >
                <Icon name="icon-[mingcute--arrow-left-line]" />
                Back to Orders
            </Link>
            </div>
          </div>
        </div>
          </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Order Items */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Status Card */}
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="p-6 border-b">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold">Order Status</h2>
                  <Chip
                    color={
                      order.status === "Completed"
                        ? "success"
                        : order.status === "Pending"
                          ? "warning"
                          : order.status === "Placed"
                            ? "secondary"
                            : order.status === "Processing"
                              ? "primary"
                              : order.status === "Delivering"
                                ? "primary"
                                : order.status === "Delivered"
                                  ? "success"
                                  : "danger"
                    }
                    variant="flat"
                  >
                    {order.status}
                  </Chip>
                </div>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Order Type</p>
                    <p className="font-medium">{order.type}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Delivery Method</p>
                    <p className="font-medium">{order.delivery}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Order Date</p>
                    <p className="font-medium">
                      {formatDate(new Date(order.createdAt), "MMM dd, yyyy hh:mm a")}
                    </p>
                  </div>
                  {order.acceptedAt && (
                    <div>
                      <p className="text-sm text-gray-500">Accepted At</p>
                      <p className="font-medium">
                        {formatDate(new Date(order.acceptedAt), "MMM dd, yyyy hh:mm a")}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Order Items */}
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="p-6 border-b">
                <h2 className="text-lg font-semibold">Order Items</h2>
              </div>
              <div className="divide-y">
              {order.items.map((item) => (
                  <div key={item.id} className="p-6">
                    <div className="flex items-center gap-4">
                      <div className="flex-1">
                        <p className="font-medium">{item.name}</p>
                        {item.details && (
                          <p className="text-sm text-gray-500">{item.details}</p>
                        )}
                        <p className="text-sm text-gray-500 mt-1">
                          KES {(item.price || 0).toLocaleString()} × {item.quantity || 0}
                        </p>
                      </div>
                      <p className="font-medium">
                        KES {((item.price || 0) * (item.quantity || 0)).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="p-6 border-t bg-gray-50">
                <div className="space-y-2">
                  {order.meta?.charges && (
                    <>
                      {order.meta.charges.AppInApp > 0 && (
                        <div className="flex justify-between items-center text-sm text-gray-500">
                          <p>App Fee</p>
                          <p>KES {order.meta.charges.AppInApp.toLocaleString()}</p>
                        </div>
                      )}
                      {order.meta.charges.Tip > 0 && (
                        <div className="flex justify-between items-center text-sm text-gray-500">
                          <p>Tip</p>
                          <p>KES {order.meta.charges.Tip.toLocaleString()}</p>
                        </div>
                      )}
                    </>
                  )}
                  <div className="flex justify-between items-center pt-2 border-t">
                    <p className="font-medium">Total Amount</p>
                    <p className="text-xl font-semibold">
                      KES {order.total.toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Customer & Payment Info */}
          <div className="space-y-6">
            {/* Customer Information */}
            {order.customer && (
              <div className="bg-white rounded-lg border shadow-sm">
                <div className="p-6 border-b">
                  <h2 className="text-lg font-semibold">Customer Information</h2>
                </div>
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    {order.customer.avatarUrl && (
                      <div className="relative w-12 h-12">
                        <Image
                          src={order.customer.avatarUrl}
                          alt={order.customer.name || "Customer"}
                          fill
                          className="rounded-full object-cover"
                        />
                      </div>
                    )}
                    <div>
                      <p className="font-medium">{order.customer.name}</p>
                      <p className="text-sm text-gray-500">{order.customer.email}</p>
                      {order.customer.phone && (
                        <p className="text-sm text-gray-500">{order.customer.phone}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Vendor Information */}
            {order.vendor && (
              <div className="bg-white rounded-lg border shadow-sm">
                <div className="p-6 border-b">
                  <h2 className="text-lg font-semibold">Vendor Information</h2>
                </div>
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    {order.vendor.logo?.url && (
                      <div className="relative w-12 h-12">
                        <Image
                          src={order.vendor.logo.url}
                          alt={order.vendor.name || "Vendor"}
                          fill
                          className="rounded-full object-cover"
                        />
                      </div>
                    )}
                    <div>
                      <p className="font-medium">{order.vendor.name}</p>
                      <p className="text-sm text-gray-500">{order.vendor.email}</p>
                      {order.vendor.phone && (
                        <p className="text-sm text-gray-500">{order.vendor.phone}</p>
                      )}
                    </div>
                  </div>
                  {order.branch?.location && (
                    <div className="mt-4 text-sm text-gray-500">
                      <p className="font-medium">Location</p>
                      <p>{order.branch.location.address}</p>
                      <p>{order.branch.location.regions.country}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Location Information */}
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="p-6 border-b">
                <h2 className="text-lg font-semibold">Location Information</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {/* Section Info */}
                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-2">Section</p>
                    {order.section ? (
                      <div className="space-y-1">
                        <p className="font-medium">{order.section.name}</p>
                        {order.section.details && (
                          <p className="text-sm text-gray-500">{order.section.details}</p>
                        )}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">No section assigned</p>
                    )}
                    {order.meta?.selectedSection && (
                      <div className="mt-1">
                        <p className="text-xs text-gray-400">Selected: {order.meta.selectedSection.title}</p>
                      </div>
                    )}
            </div>

                  {/* Lot Info */}
                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-2">Lot</p>
                    {order.lotId ? (
                      <p className="font-medium">{order.lotId}</p>
                    ) : (
                      <p className="text-sm text-gray-500">No lot assigned</p>
                    )}
                    {order.meta?.selectedTable && (
                      <div className="mt-1">
                        <p className="text-xs text-gray-400">Selected Table: {order.meta.selectedTable.title}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
